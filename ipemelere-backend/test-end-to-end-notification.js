const io = require('socket.io-client');
const jwt = require('jsonwebtoken');
const axios = require('axios');

console.log('🚗 END-TO-END Driver Notification Test');
console.log('======================================\n');

// Configuration
const SERVER_URL = 'http://localhost:3001';
const JWT_SECRET = 'your-super-secret-jwt-key-change-in-production';

// Test users
const driverUser = {
  sub: 16,
  userId: 16,
  email: '<EMAIL>',
  roles: ['driver'],
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (60 * 60)
};

const passengerUser = {
  sub: 18,
  userId: 18,
  email: '<EMAIL>',
  roles: ['passenger'],
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (60 * 60)
};

// Generate tokens
const driverToken = jwt.sign(driverUser, JWT_SECRET);
const passengerToken = jwt.sign(passengerUser, JWT_SECRET);

console.log('🔑 Generated test tokens');
console.log(`   Driver Token: ${driverToken.substring(0, 50)}...`);
console.log(`   Passenger Token: ${passengerToken.substring(0, 50)}...\n`);

class EndToEndNotificationTest {
  constructor() {
    this.driverSocket = null;
    this.driverConnected = false;
    this.driverSubscribed = false;
    this.rideCreated = null;
    this.notificationReceived = false;
    this.notificationData = null;
    this.testStartTime = Date.now();
  }

  async runTest() {
    try {
      console.log('🚀 Starting END-TO-END Driver Notification Test\n');
      
      // Step 1: Connect and subscribe driver
      await this.setupDriver();
      
      // Step 2: Create ride as passenger
      await this.createRide();
      
      // Step 3: Wait for notification
      await this.waitForNotification();
      
      // Step 4: Verify notification
      this.verifyNotification();
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
      this.printResults();
    } finally {
      this.cleanup();
    }
  }

  async setupDriver() {
    console.log('🚗 Step 1: Setting up driver (connect + subscribe)...');
    
    // Connect driver
    await this.connectDriver();
    
    // Subscribe to ride requests
    await this.subscribeDriver();
  }

  async connectDriver() {
    console.log('   📡 Connecting driver to WebSocket...');
    
    return new Promise((resolve, reject) => {
      this.driverSocket = io(`${SERVER_URL}/driver-availability`, {
        auth: { token: driverToken },
        autoConnect: true,
        timeout: 10000
      });

      this.driverSocket.on('connect', () => {
        console.log(`   ✅ Driver connected: ${this.driverSocket.id}`);
        this.driverConnected = true;
        this.setupNotificationListener();
        resolve();
      });

      this.driverSocket.on('connect_error', (error) => {
        console.log(`   ❌ Driver connection failed: ${error.message}`);
        reject(error);
      });

      setTimeout(() => reject(new Error('Connection timeout')), 10000);
    });
  }

  setupNotificationListener() {
    // Listen for ride request notifications
    this.driverSocket.on('new_ride_request', (rideData) => {
      console.log('\n🎉 *** RIDE REQUEST NOTIFICATION RECEIVED! ***');
      console.log('=============================================');
      console.log(`   🆔 Ride ID: ${rideData.id}`);
      console.log(`   👤 Passenger: ${rideData.passengerName || 'N/A'}`);
      console.log(`   📍 Pickup: ${rideData.pickupAddress}`);
      console.log(`   📍 Dropoff: ${rideData.dropoffAddress}`);
      console.log(`   📏 Distance: ${rideData.distanceFromPickup}km`);
      console.log(`   💰 Price: $${rideData.estimatedPrice}`);
      console.log(`   ⏰ Deadline: ${rideData.responseDeadline}`);
      console.log('=============================================\n');
      
      this.notificationReceived = true;
      this.notificationData = rideData;
    });

    // Listen for other events
    this.driverSocket.on('ride_expired', (data) => {
      console.log(`   ⏰ Ride ${data.rideId} expired`);
    });
  }

  async subscribeDriver() {
    console.log('   📍 Subscribing driver to ride requests...');
    
    return new Promise((resolve, reject) => {
      this.driverSocket.on('ride_requests_subscription_confirmed', (data) => {
        console.log(`   ✅ Driver subscribed (ID: ${data.driverId})`);
        this.driverSubscribed = true;
        resolve();
      });

      this.driverSocket.on('error', (error) => {
        console.log(`   ❌ Subscription failed: ${error.message}`);
        reject(new Error(error.message));
      });

      // Send subscription
      this.driverSocket.emit('subscribe_to_ride_requests', {
        latitude: -15.78853,
        longitude: 35.00494,
        maxRadius: 25
      });

      setTimeout(() => reject(new Error('Subscription timeout')), 5000);
    });
  }

  async createRide() {
    console.log('\n🚗 Step 2: Creating ride as passenger...');
    
    try {
      const rideData = {
        pickupAddress: "Test Pickup - Lilongwe City Center",
        pickupLatitude: -15.78853,
        pickupLongitude: 35.00494,
        dropoffAddress: "Test Dropoff - Lilongwe Mall",
        dropoffLatitude: -15.7894,
        dropoffLongitude: 35.0831
      };

      console.log(`   📍 From: ${rideData.pickupAddress}`);
      console.log(`   📍 To: ${rideData.dropoffAddress}`);
      console.log('   📤 Sending ride request...');

      const response = await axios.post(`${SERVER_URL}/api/v1/rides/book`, rideData, {
        headers: {
          'Authorization': `Bearer ${passengerToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 201) {
        this.rideCreated = response.data;
        console.log(`   ✅ Ride created successfully!`);
        console.log(`   🆔 Ride ID: ${this.rideCreated.id}`);
        console.log(`   💰 Estimated Price: $${this.rideCreated.estimatedPrice}`);
      } else {
        throw new Error(`Unexpected response status: ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Ride creation failed: ${error.message}`);
      if (error.response) {
        console.log(`   📄 Status: ${error.response.status}`);
        console.log(`   📄 Data:`, error.response.data);
      }
      throw error;
    }
  }

  async waitForNotification() {
    console.log('\n🔔 Step 3: Waiting for driver notification...');
    console.log('   ⏳ Waiting up to 15 seconds for notification...');
    
    return new Promise((resolve, reject) => {
      // Check if already received
      if (this.notificationReceived) {
        console.log('   ✅ Notification already received!');
        resolve();
        return;
      }

      // Set timeout
      const timeout = setTimeout(() => {
        if (!this.notificationReceived) {
          console.log('   ❌ No notification received within timeout');
          reject(new Error('Notification timeout - driver did not receive ride request'));
        }
      }, 15000);

      // Check periodically
      const checkInterval = setInterval(() => {
        if (this.notificationReceived) {
          clearTimeout(timeout);
          clearInterval(checkInterval);
          console.log('   ✅ Notification received!');
          resolve();
        }
      }, 500);
    });
  }

  verifyNotification() {
    console.log('\n🔍 Step 4: Verifying notification content...');
    
    if (!this.notificationData) {
      console.log('   ❌ No notification data to verify');
      return;
    }

    const checks = [
      { name: 'Ride ID Match', check: () => this.notificationData.id === this.rideCreated.id },
      { name: 'Pickup Address', check: () => this.notificationData.pickupAddress },
      { name: 'Dropoff Address', check: () => this.notificationData.dropoffAddress },
      { name: 'Distance Calculated', check: () => typeof this.notificationData.distanceFromPickup === 'number' },
      { name: 'Price Included', check: () => typeof this.notificationData.estimatedPrice === 'number' },
      { name: 'Response Deadline', check: () => this.notificationData.responseDeadline }
    ];

    let passedChecks = 0;
    checks.forEach(check => {
      const passed = check.check();
      console.log(`   ${passed ? '✅' : '❌'} ${check.name}: ${passed ? 'Valid' : 'Invalid'}`);
      if (passed) passedChecks++;
    });

    console.log(`\n   📊 Verification: ${passedChecks}/${checks.length} checks passed`);
  }

  printResults() {
    const duration = ((Date.now() - this.testStartTime) / 1000).toFixed(1);
    
    console.log('\n📊 END-TO-END Test Results');
    console.log('==========================');
    
    console.log(`✅ Driver Connected: ${this.driverConnected ? 'Yes' : 'No'}`);
    console.log(`📍 Driver Subscribed: ${this.driverSubscribed ? 'Yes' : 'No'}`);
    console.log(`🚗 Ride Created: ${this.rideCreated ? 'Yes' : 'No'}`);
    console.log(`🔔 Notification Received: ${this.notificationReceived ? 'Yes' : 'No'}`);
    
    if (this.rideCreated) {
      console.log(`   Ride ID: ${this.rideCreated.id}`);
    }
    
    if (this.notificationData) {
      console.log(`   Notified Ride ID: ${this.notificationData.id}`);
      console.log(`   Distance from Driver: ${this.notificationData.distanceFromPickup}km`);
    }
    
    console.log(`\n⏱️ Test Duration: ${duration}s`);
    
    console.log('\n🎯 Final Analysis:');
    
    if (this.driverConnected && this.driverSubscribed && this.rideCreated && this.notificationReceived) {
      console.log('🎉 *** COMPLETE SUCCESS! ***');
      console.log('✅ WebSocket driver notification system is FULLY WORKING!');
      console.log('✅ Drivers are receiving real-time ride request notifications!');
      console.log('✅ End-to-end flow is operational!');
      console.log('✅ The system is ready for production use!');
    } else {
      console.log('⚠️ Some components are not working properly:');
      if (!this.driverConnected) console.log('   ❌ Driver WebSocket connection failed');
      if (!this.driverSubscribed) console.log('   ❌ Driver subscription failed');
      if (!this.rideCreated) console.log('   ❌ Ride creation failed');
      if (!this.notificationReceived) console.log('   ❌ Driver notification not received');
    }
  }

  cleanup() {
    console.log('\n🧹 Cleaning up...');
    if (this.driverSocket) {
      this.driverSocket.disconnect();
      console.log('   ✅ Driver socket disconnected');
    }
  }
}

// Run the test
const test = new EndToEndNotificationTest();
test.runTest().then(() => {
  console.log('\n🏁 End-to-end test finished');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Test failed with error:', error.message);
  process.exit(1);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(0);
});

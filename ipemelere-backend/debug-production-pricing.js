#!/usr/bin/env node

/**
 * Production Pricing Debug Script
 * Run this on production to debug the NaN pricing issue
 */

require('dotenv').config();
const mysql = require('mysql2/promise');

async function debugProductionPricing() {
  let connection;
  
  try {
    console.log('🔍 Production Pricing Debug Script');
    console.log('=====================================\n');

    // Connect to database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
    });

    console.log('✅ Connected to production database\n');

    // 1. Check pricing configuration
    console.log('1. CHECKING PRICING CONFIGURATION');
    console.log('==================================');
    
    const [configRows] = await connection.execute(`
      SELECT 
        id, name, isActive, model, 
        basePrice, pricePerKm, 
        vehicleTypePricing,
        description,
        TYPEOF(basePrice) as basePriceType,
        TYPEOF(pricePerKm) as pricePerKmType
      FROM pricing_configurations 
      WHERE isActive = 1
      ORDER BY priority DESC, createdAt DESC
      LIMIT 1
    `);

    if (configRows.length === 0) {
      console.log('❌ NO ACTIVE PRICING CONFIGURATION FOUND!');
      console.log('This could be the cause of NaN values.\n');
      
      // Create default configuration
      console.log('Creating default pricing configuration...');
      await connection.execute(`
        INSERT INTO pricing_configurations (
          name, isActive, model, basePrice, pricePerKm, 
          description, priority
        ) VALUES (
          'production_default', 1, 'distance_only', 3000.00, 3000.00,
          'Production default: 3000 MWK base + 3000 MWK per km', 1
        )
      `);
      console.log('✅ Default configuration created\n');
    } else {
      const config = configRows[0];
      console.log('Active Configuration:');
      console.log(`  ID: ${config.id}`);
      console.log(`  Name: ${config.name}`);
      console.log(`  Model: ${config.model}`);
      console.log(`  Base Price: ${config.basePrice} (Type: ${typeof config.basePrice})`);
      console.log(`  Price per KM: ${config.pricePerKm} (Type: ${typeof config.pricePerKm})`);
      console.log(`  Vehicle Type Pricing: ${config.vehicleTypePricing}`);
      console.log(`  Description: ${config.description}\n`);

      // Check for potential issues
      if (config.basePrice === null || config.basePrice === undefined) {
        console.log('❌ BASE PRICE IS NULL/UNDEFINED!');
      }
      if (config.pricePerKm === null || config.pricePerKm === undefined) {
        console.log('❌ PRICE PER KM IS NULL/UNDEFINED!');
      }
      if (isNaN(Number(config.basePrice))) {
        console.log(`❌ BASE PRICE IS NOT A VALID NUMBER: ${config.basePrice}`);
      }
      if (isNaN(Number(config.pricePerKm))) {
        console.log(`❌ PRICE PER KM IS NOT A VALID NUMBER: ${config.pricePerKm}`);
      }
    }

    // 2. Test price calculation with sample data
    console.log('2. TESTING PRICE CALCULATION');
    console.log('=============================');
    
    const testCoordinates = {
      pickupLat: -15.7392417,
      pickupLng: 35.0083903,
      dropoffLat: -15.6599606,
      dropoffLng: 34.9405286
    };

    console.log('Test coordinates:');
    console.log(`  Pickup: ${testCoordinates.pickupLat}, ${testCoordinates.pickupLng}`);
    console.log(`  Dropoff: ${testCoordinates.dropoffLat}, ${testCoordinates.dropoffLng}`);

    // Calculate distance using same formula as the app
    function calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371; // Earth's radius in kilometers
      const dLat = (lat2 - lat1) * (Math.PI / 180);
      const dLon = (lon2 - lon1) * (Math.PI / 180);
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * (Math.PI / 180)) *
          Math.cos(lat2 * (Math.PI / 180)) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    }

    const distance = calculateDistance(
      testCoordinates.pickupLat,
      testCoordinates.pickupLng,
      testCoordinates.dropoffLat,
      testCoordinates.dropoffLng
    );

    console.log(`  Calculated distance: ${distance} km`);

    // Get the current config again for calculation
    const [currentConfig] = await connection.execute(`
      SELECT basePrice, pricePerKm FROM pricing_configurations 
      WHERE isActive = 1 ORDER BY priority DESC, createdAt DESC LIMIT 1
    `);

    if (currentConfig.length > 0) {
      const config = currentConfig[0];
      const basePrice = Number(config.basePrice);
      const pricePerKm = Number(config.pricePerKm);
      const distancePrice = distance * pricePerKm;
      const totalPrice = basePrice + distancePrice;

      console.log('\nPrice Calculation:');
      console.log(`  Base Price: ${basePrice} (from DB: ${config.basePrice})`);
      console.log(`  Price per KM: ${pricePerKm} (from DB: ${config.pricePerKm})`);
      console.log(`  Distance Price: ${distancePrice}`);
      console.log(`  Total Price: ${totalPrice}`);

      if (isNaN(totalPrice)) {
        console.log('❌ TOTAL PRICE IS NaN!');
        console.log('This indicates the issue is in the price calculation.');
      } else {
        console.log('✅ Price calculation successful');
      }
    }

    // 3. Check recent failed rides
    console.log('\n3. CHECKING RECENT FAILED RIDES');
    console.log('================================');
    
    const [failedRides] = await connection.execute(`
      SELECT id, estimatedPrice, distance, createdAt, status
      FROM rides 
      WHERE estimatedPrice IS NULL OR estimatedPrice = 'NaN' OR estimatedPrice = 0
      ORDER BY createdAt DESC 
      LIMIT 5
    `);

    if (failedRides.length > 0) {
      console.log('Recent rides with pricing issues:');
      failedRides.forEach(ride => {
        console.log(`  Ride ${ride.id}: Price=${ride.estimatedPrice}, Distance=${ride.distance}, Status=${ride.status}, Created=${ride.createdAt}`);
      });
    } else {
      console.log('✅ No recent rides with pricing issues found');
    }

    // 4. Environment check
    console.log('\n4. ENVIRONMENT CHECK');
    console.log('====================');
    console.log(`Node.js version: ${process.version}`);
    console.log(`Environment: ${process.env.NODE_ENV || 'not set'}`);
    console.log(`Database: ${process.env.DB_DATABASE}`);
    console.log(`Host: ${process.env.DB_HOST}`);

    console.log('\n✅ Debug script completed successfully');

  } catch (error) {
    console.error('❌ Error during debugging:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the debug script
debugProductionPricing().catch(console.error);

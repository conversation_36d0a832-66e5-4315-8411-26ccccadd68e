const io = require('socket.io-client');
const jwt = require('jsonwebtoken');
const axios = require('axios');

console.log('🎯 COMPLETE Driver Notification Flow Test');
console.log('==========================================\n');

// Configuration
const SERVER_URL = 'http://localhost:3001';
const JWT_SECRET = 'your-super-secret-jwt-key-change-in-production';

// Test users
const driverUser = {
  sub: 16,
  userId: 16,
  email: '<EMAIL>',
  roles: ['driver'],
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (60 * 60)
};

const passengerUser = {
  sub: 18,
  userId: 18,
  email: '<EMAIL>',
  roles: ['passenger'],
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (60 * 60)
};

// Generate tokens
const driverToken = jwt.sign(driverUser, JWT_SECRET);
const passengerToken = jwt.sign(passengerUser, JWT_SECRET);

console.log('🔑 Generated test tokens');
console.log(`   Driver Token: ${driverToken.substring(0, 50)}...`);
console.log(`   Passenger Token: ${passengerToken.substring(0, 50)}...\n`);

class CompleteDriverNotificationFlowTest {
  constructor() {
    this.driverSocket = null;
    this.driverConnected = false;
    this.driverSubscribed = false;
    this.rideCreated = null;
    this.notificationReceived = false;
    this.notificationData = null;
    this.testStartTime = Date.now();
    this.keepAlive = true;
  }

  async runTest() {
    try {
      console.log('🚀 Starting COMPLETE Driver Notification Flow Test\n');
      
      // Step 1: Connect driver and keep alive
      await this.connectDriverAndKeepAlive();
      
      // Step 2: Subscribe driver
      await this.subscribeDriver();
      
      // Step 3: Wait for stability
      await this.waitForStability();
      
      // Step 4: Create ride with correct API format
      await this.createRideWithCorrectFormat();
      
      // Step 5: Wait for notification
      await this.waitForNotification();
      
      // Step 6: Verify notification content
      this.verifyNotificationContent();
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
      this.printResults();
    } finally {
      this.cleanup();
    }
  }

  async connectDriverAndKeepAlive() {
    console.log('🔌 Step 1: Connecting driver and keeping alive...');
    
    return new Promise((resolve, reject) => {
      this.driverSocket = io(`${SERVER_URL}/driver-availability`, {
        auth: { token: driverToken },
        autoConnect: true,
        timeout: 10000,
        forceNew: true
      });

      this.driverSocket.on('connect', () => {
        console.log(`   ✅ Driver connected: ${this.driverSocket.id}`);
        this.driverConnected = true;
        this.setupEventListeners();
        this.startHeartbeat();

        // Wait a bit for authentication to complete before resolving
        setTimeout(() => {
          resolve();
        }, 1000);
      });

      this.driverSocket.on('connect_error', (error) => {
        console.log(`   ❌ Driver connection failed: ${error.message}`);
        reject(error);
      });

      this.driverSocket.on('disconnect', (reason) => {
        console.log(`   ⚠️ Driver disconnected: ${reason}`);
        this.driverConnected = false;
      });

      setTimeout(() => reject(new Error('Connection timeout')), 10000);
    });
  }

  startHeartbeat() {
    // Send periodic heartbeat to keep connection alive
    const heartbeatInterval = setInterval(() => {
      if (this.driverSocket && this.driverSocket.connected && this.keepAlive) {
        this.driverSocket.emit('heartbeat_response', { timestamp: Date.now() });
      } else {
        clearInterval(heartbeatInterval);
      }
    }, 5000); // Every 5 seconds
  }

  setupEventListeners() {
    console.log('   🎧 Setting up event listeners...');
    
    // Listen for ride request notifications - THIS IS THE KEY TEST!
    this.driverSocket.on('new_ride_request', (rideData) => {
      console.log('\n🎉 *** RIDE REQUEST NOTIFICATION RECEIVED! ***');
      console.log('=============================================');
      console.log(`   🆔 Ride ID: ${rideData.rideId}`);
      console.log(`   👤 Passenger: ${rideData.passengerName || 'N/A'}`);
      console.log(`   📍 Pickup: ${rideData.pickupAddress}`);
      console.log(`   📍 Dropoff: ${rideData.dropoffAddress}`);
      console.log(`   📏 Distance: ${rideData.distanceFromDriver}km`);
      console.log(`   💰 Price: $${rideData.estimatedPrice}`);
      console.log(`   ⏰ Deadline: ${rideData.responseDeadline}`);
      console.log('=============================================\n');

      this.notificationReceived = true;
      this.notificationData = rideData;
    });

    // Listen for subscription confirmation
    this.driverSocket.on('ride_requests_subscription_confirmed', (data) => {
      console.log(`   ✅ Subscription confirmed (Driver ID: ${data.driverId})`);
      this.driverSubscribed = true;
    });

    // Listen for connection established
    this.driverSocket.on('connection_established', (data) => {
      console.log(`   📡 Connection established (Driver ID: ${data.driverId})`);
    });

    // Listen for errors
    this.driverSocket.on('error', (error) => {
      console.log(`   ❌ Error: ${error.message || error}`);
    });

    console.log('   ✅ Event listeners set up');
  }

  async subscribeDriver() {
    console.log('\n📍 Step 2: Subscribing driver to ride requests...');
    
    return new Promise((resolve, reject) => {
      if (!this.driverConnected) {
        reject(new Error('Driver not connected'));
        return;
      }

      // Send subscription
      console.log('   📤 Sending subscription request...');
      this.driverSocket.emit('subscribe_to_ride_requests', {
        latitude: -15.78853,
        longitude: 35.00494,
        maxRadius: 25
      });

      // Wait for confirmation
      const timeout = setTimeout(() => {
        if (!this.driverSubscribed) {
          console.log('   ⚠️ Subscription timeout, but continuing...');
        }
        resolve();
      }, 5000);

      // If subscription confirmed early, resolve
      const checkInterval = setInterval(() => {
        if (this.driverSubscribed) {
          clearTimeout(timeout);
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
    });
  }

  async waitForStability() {
    console.log('\n⏱️ Step 3: Waiting for connection stability...');
    
    return new Promise((resolve) => {
      let seconds = 0;
      const maxWait = 2;
      
      const interval = setInterval(() => {
        seconds++;
        console.log(`   ⏳ Stabilizing... ${seconds}/${maxWait}s (Connected: ${this.driverConnected})`);
        
        if (seconds >= maxWait) {
          clearInterval(interval);
          console.log('   ✅ Connection stable');
          resolve();
        }
      }, 1000);
    });
  }

  async createRideWithCorrectFormat() {
    console.log('\n🚗 Step 4: Creating ride with correct API format...');
    
    if (!this.driverConnected) {
      throw new Error('Driver not connected - cannot test notification');
    }

    try {
      // Use the correct API format based on BookRideDto
      const rideData = {
        optionId: "sedan_standard", // Required
        pickupLocation: "COMPLETE TEST Pickup - Lilongwe City Center", // Required
        pickupLatitude: -15.78853, // Required
        pickupLongitude: 35.00494, // Required
        dropoffLocation: "COMPLETE TEST Dropoff - Lilongwe Mall", // Required
        dropoffLatitude: -15.7894, // Required
        dropoffLongitude: 35.0831, // Required
        paymentMethod: "cash" // Required - must be one of: cash, card, mobile_money, wallet
      };

      console.log(`   📍 From: ${rideData.pickupLocation}`);
      console.log(`   📍 To: ${rideData.dropoffLocation}`);
      console.log(`   🚗 Option: ${rideData.optionId}`);
      console.log(`   💳 Payment: ${rideData.paymentMethod}`);
      console.log('   📤 Sending ride request...');

      const response = await axios.post(`${SERVER_URL}/api/v1/rides/book`, rideData, {
        headers: {
          'Authorization': `Bearer ${passengerToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 201) {
        this.rideCreated = response.data;
        console.log(`   ✅ Ride created successfully!`);
        console.log(`   🆔 Booking ID: ${this.rideCreated.bookingId}`);
        console.log(`   📊 Status: ${this.rideCreated.status}`);
        console.log(`   ⏰ ETA: ${this.rideCreated.eta}`);
      } else {
        throw new Error(`Unexpected response status: ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Ride creation failed: ${error.message}`);
      if (error.response) {
        console.log(`   📄 Status: ${error.response.status}`);
        console.log(`   📄 Data:`, JSON.stringify(error.response.data, null, 2));
      }
      throw error;
    }
  }

  async waitForNotification() {
    console.log('\n🔔 Step 5: Waiting for driver notification...');
    console.log('   ⏳ Waiting up to 25 seconds for notification...');
    
    return new Promise((resolve, reject) => {
      // Check if already received
      if (this.notificationReceived) {
        console.log('   ✅ Notification already received!');
        resolve();
        return;
      }

      // Set timeout
      const timeout = setTimeout(() => {
        if (!this.notificationReceived) {
          console.log('   ❌ No notification received within timeout');
          reject(new Error('Notification timeout - driver did not receive ride request'));
        }
      }, 25000);

      // Check periodically
      const checkInterval = setInterval(() => {
        console.log(`   ⏳ Still waiting... (Connected: ${this.driverConnected}, Subscribed: ${this.driverSubscribed})`);
        
        if (this.notificationReceived) {
          clearTimeout(timeout);
          clearInterval(checkInterval);
          console.log('   ✅ Notification received!');
          resolve();
        }
      }, 3000);
    });
  }

  verifyNotificationContent() {
    console.log('\n🔍 Step 6: Verifying notification content...');
    
    if (!this.notificationData) {
      console.log('   ❌ No notification data to verify');
      return;
    }

    const checks = [
      { name: 'Ride ID Present', check: () => this.notificationData.rideId },
      { name: 'Pickup Location', check: () => this.notificationData.pickupAddress },
      { name: 'Dropoff Location', check: () => this.notificationData.dropoffAddress },
      { name: 'Distance Calculated', check: () => typeof this.notificationData.distanceFromDriver === 'number' },
      { name: 'Price Included', check: () => typeof this.notificationData.estimatedPrice === 'number' },
      { name: 'Response Deadline', check: () => this.notificationData.responseDeadline }
    ];

    let passedChecks = 0;
    checks.forEach(check => {
      const passed = check.check();
      const result = passed ? 'Valid' : 'Invalid';
      console.log(`   ${passed ? '✅' : '❌'} ${check.name}: ${result}`);
      if (passed) passedChecks++;
    });

    console.log(`\n   📊 Verification: ${passedChecks}/${checks.length} checks passed`);
  }

  printResults() {
    const duration = ((Date.now() - this.testStartTime) / 1000).toFixed(1);
    
    console.log('\n📊 COMPLETE Driver Notification Flow Results');
    console.log('============================================');
    
    console.log(`✅ Driver Connected: ${this.driverConnected ? 'Yes' : 'No'}`);
    console.log(`📍 Driver Subscribed: ${this.driverSubscribed ? 'Yes' : 'No'}`);
    console.log(`🚗 Ride Created: ${this.rideCreated ? 'Yes' : 'No'}`);
    console.log(`🔔 Notification Received: ${this.notificationReceived ? 'Yes' : 'No'}`);
    
    if (this.rideCreated) {
      console.log(`   Booking ID: ${this.rideCreated.bookingId}`);
      console.log(`   Status: ${this.rideCreated.status}`);
    }
    
    if (this.notificationData) {
      console.log(`   Notified Ride ID: ${this.notificationData.rideId}`);
      console.log(`   Distance from Driver: ${this.notificationData.distanceFromDriver}km`);
    }
    
    console.log(`\n⏱️ Test Duration: ${duration}s`);
    
    console.log('\n🎯 FINAL VERDICT:');
    
    if (this.notificationReceived && this.rideCreated) {
      console.log('🎉 *** COMPLETE SUCCESS! ***');
      console.log('✅ WebSocket driver notification system is FULLY WORKING!');
      console.log('✅ Drivers ARE receiving real-time ride request notifications!');
      console.log('✅ End-to-end flow is operational!');
      console.log('✅ The system is ready for production use!');
      console.log('✅ MISSION ACCOMPLISHED! 🚀');
      console.log('\n🔥 PROOF: Driver received ride request notification via WebSocket!');
    } else if (this.driverConnected && this.driverSubscribed && this.rideCreated) {
      console.log('⚠️ Partial Success:');
      console.log('✅ WebSocket connection and subscription work');
      console.log('✅ Ride creation works');
      console.log('❌ Notification not received - may be server-side notification logic issue');
      console.log('🔧 WebSocket infrastructure is proven to work');
    } else {
      console.log('❌ System has issues that need to be addressed');
      if (!this.driverConnected) console.log('   ❌ Driver WebSocket connection failed');
      if (!this.driverSubscribed) console.log('   ❌ Driver subscription failed');
      if (!this.rideCreated) console.log('   ❌ Ride creation failed');
    }
  }

  cleanup() {
    console.log('\n🧹 Cleaning up...');
    this.keepAlive = false;
    if (this.driverSocket) {
      this.driverSocket.disconnect();
      console.log('   ✅ Driver socket disconnected');
    }
  }
}

// Run the test
const test = new CompleteDriverNotificationFlowTest();
test.runTest().then(() => {
  console.log('\n🏁 Complete driver notification flow test finished');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Test failed with error:', error.message);
  process.exit(1);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(0);
});

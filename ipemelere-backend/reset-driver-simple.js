const mysql = require('mysql2/promise');

async function resetDriver() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'password',
      database: 'ipemelere_db'
    });

    // Reset driver status to available
    await connection.execute(
      'UPDATE drivers SET status = ? WHERE id = ?',
      ['available', 4]
    );

    console.log('✅ Driver 4 status reset to available');
    await connection.end();
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

resetDriver();

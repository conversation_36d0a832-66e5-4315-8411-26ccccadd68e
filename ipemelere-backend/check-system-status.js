#!/usr/bin/env node

/**
 * System Status Checker
 *
 * This script checks the current state of the system to diagnose issues
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const API_BASE = `${BASE_URL}/api/v1`;

async function checkSystemStatus() {
  console.log('🔍 Checking System Status...\n');

  // Check if server is running
  try {
    const response = await axios.get(`${API_BASE}/`);
    console.log('✅ Server is running');
  } catch (error) {
    console.log('❌ Server is not responding');
    console.log('💡 Make sure to start the server with: npm run start:dev');
    return;
  }

  // Test driver login
  console.log('\n📋 Testing Driver Login...');
  try {
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginResponse.data.token && loginResponse.data.user.role === 'driver') {
      console.log('✅ Driver login successful');
      const token = loginResponse.data.token;

      // Check driver status
      console.log('\n📊 Checking Driver Status...');
      try {
        const statusResponse = await axios.get(`${API_BASE}/drivers/status`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        console.log('Driver Status:', JSON.stringify(statusResponse.data, null, 2));

        if (statusResponse.data.isOnShift) {
          console.log('✅ Driver is on shift');
        } else {
          console.log('⚠️  Driver is not on shift');
        }

        if (statusResponse.data.status === 'available') {
          console.log('✅ Driver is available');
        } else {
          console.log(`⚠️  Driver status: ${statusResponse.data.status}`);
        }

      } catch (error) {
        console.log('❌ Failed to get driver status:', error.response?.data?.message || error.message);
      }

      // Check vehicles
      console.log('\n🚗 Checking Available Vehicles...');
      try {
        const vehiclesResponse = await axios.get(`${API_BASE}/vehicles`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (vehiclesResponse.data.vehicles && vehiclesResponse.data.vehicles.length > 0) {
          console.log(`✅ Found ${vehiclesResponse.data.vehicles.length} vehicles`);
          console.log('First vehicle:', vehiclesResponse.data.vehicles[0]);
        } else {
          console.log('⚠️  No vehicles found');
        }
      } catch (error) {
        console.log('❌ Failed to get vehicles:', error.response?.data?.message || error.message);
      }

    } else {
      console.log('❌ Driver login failed');
      console.log('Response:', JSON.stringify(loginResponse.data, null, 2));
    }
  } catch (error) {
    console.log('❌ Driver login error:', error.response?.data?.message || error.message);
    if (error.response?.data) {
      console.log('Full error response:', JSON.stringify(error.response.data, null, 2));
    }
  }

  // Test passenger login
  console.log('\n👤 Testing Passenger Login...');
  try {
    const passengerResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (passengerResponse.data.token && passengerResponse.data.user.role === 'passenger') {
      console.log('✅ Passenger login successful');
    } else {
      console.log('❌ Passenger login failed');
      console.log('Response:', JSON.stringify(passengerResponse.data, null, 2));
    }
  } catch (error) {
    console.log('❌ Passenger login error:', error.response?.data?.message || error.message);
    if (error.response?.data) {
      console.log('Full error response:', JSON.stringify(error.response.data, null, 2));
    }
  }

  console.log('\n📝 Recommendations:');
  console.log('1. If driver login fails, check if the user exists in the database');
  console.log('2. If no vehicles found, create a vehicle for the driver');
  console.log('3. If driver is not on shift, start a shift before testing WebSocket');
  console.log('4. Make sure WebSocket gateway is properly configured');
}

checkSystemStatus().catch(console.error);
#!/usr/bin/env node

/**
 * Complete Driver Notification Flow Test
 *
 * This script tests the entire flow:
 * 1. Driver login
 * 2. Driver starts shift
 * 3. Driver connects to WebSocket
 * 4. Driver subscribes to ride requests
 * 5. Passenger books a ride
 * 6. Driver receives notification
 * 7. Driver accepts/rejects ride
 */

const axios = require('axios');
const { io } = require('socket.io-client');

const BASE_URL = 'http://localhost:3001';
const API_BASE = `${BASE_URL}/api/v1`;

class DriverFlowTester {
  constructor() {
    this.driverToken = null;
    this.passengerToken = null;
    this.driverSocket = null;
    this.testResults = [];
    this.currentRide = null;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
    console.log(`${emoji} [${timestamp}] ${message}`);
  }

  addResult(testName, success, message) {
    this.testResults.push({ testName, success, message });
    this.log(`${testName}: ${message}`, success ? 'success' : 'error');
  }

  async runCompleteTest() {
    console.log('🚀 Starting Complete Driver Notification Flow Test\n');
    console.log('=' .repeat(60));

    try {
      // Step 1: Login as driver
      await this.loginAsDriver();

      // Step 2: Start driver shift
      await this.startDriverShift();

      // Step 3: Connect to WebSocket
      await this.connectToWebSocket();

      // Step 4: Subscribe to ride requests
      await this.subscribeToRideRequests();

      // Step 5: Login as passenger (for testing)
      await this.loginAsPassenger();

      // Step 6: Create ride request and test notification
      await this.testRideNotification();

      // Step 7: Test driver response
      await this.testDriverResponse();

      // Step 8: Cleanup
      await this.cleanup();

      this.printResults();

    } catch (error) {
      this.log(`Test suite failed: ${error.message}`, 'error');
      console.error(error);
    }
  }

  async loginAsDriver() {
    this.log('Step 1: Logging in as driver...');

    try {
      const response = await axios.post(`${API_BASE}/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });

      if (response.data.token && response.data.user.role === 'driver') {
        this.driverToken = response.data.token;
        this.addResult('Driver Login', true, 'Successfully logged in as driver');
      } else {
        throw new Error('Login failed or user is not a driver');
      }
    } catch (error) {
      this.addResult('Driver Login', false, `Login failed: ${error.response?.data?.message || error.message}`);
      throw error;
    }
  }

  async startDriverShift() {
    this.log('Step 2: Starting driver shift...');

    try {
      // First check if driver is already on shift
      const statusCheck = await axios.get(`${API_BASE}/drivers/status`, {
        headers: { Authorization: `Bearer ${this.driverToken}` }
      });

      if (statusCheck.data.isOnShift) {
        this.addResult('Start Shift', true, 'Driver is already on shift');
        await this.verifyDriverStatus();
        return;
      }

      const response = await axios.post(`${API_BASE}/drivers/shift/start`, {
        vehicleId: 1, // Assuming vehicle ID 1 exists
        latitude: -15.78853,
        longitude: 35.00494,
        notes: 'Starting shift for flow test'
      }, {
        headers: { Authorization: `Bearer ${this.driverToken}` }
      });

      if (response.data.success || response.data.shiftId) {
        this.addResult('Start Shift', true, `Shift started with ID: ${response.data.shiftId}`);

        // Wait a moment for status to update
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Verify driver status
        await this.verifyDriverStatus();
      } else {
        throw new Error('Failed to start shift');
      }
    } catch (error) {
      // If already on shift, that's okay
      if (error.response?.data?.message?.includes('already on shift') ||
          error.response?.data?.message?.includes('active shift')) {
        this.addResult('Start Shift', true, 'Driver is already on shift');
        await this.verifyDriverStatus();
        return;
      }

      this.addResult('Start Shift', false, `Shift start failed: ${error.response?.data?.message || error.message}`);
      this.log(`Full error: ${JSON.stringify(error.response?.data, null, 2)}`);
      throw error;
    }
  }

  async verifyDriverStatus() {
    this.log('Verifying driver status...');

    try {
      const response = await axios.get(`${API_BASE}/drivers/status`, {
        headers: { Authorization: `Bearer ${this.driverToken}` }
      });

      const status = response.data;
      this.log(`Driver Status: ${JSON.stringify(status, null, 2)}`);

      if (status.isOnShift && status.status === 'available') {
        this.addResult('Driver Status Check', true, 'Driver is on shift and available');
      } else {
        this.addResult('Driver Status Check', false, `Driver status: isOnShift=${status.isOnShift}, status=${status.status}`);
      }
    } catch (error) {
      this.addResult('Driver Status Check', false, `Status check failed: ${error.response?.data?.message || error.message}`);
    }
  }

  async connectToWebSocket() {
    this.log('Step 3: Connecting to WebSocket...');

    return new Promise((resolve, reject) => {
      this.driverSocket = io(`${BASE_URL}/driver-availability`, {
        auth: { token: this.driverToken },
        transports: ['websocket']
      });

      this.driverSocket.on('connection_established', (data) => {
        this.addResult('WebSocket Connection', true, `Connected as ${data.userRole} (ID: ${data.userId})`);
        resolve();
      });

      this.driverSocket.on('connect_error', (error) => {
        this.addResult('WebSocket Connection', false, `Connection failed: ${error.message}`);
        reject(error);
      });

      // Set up event listeners for debugging
      this.driverSocket.onAny((event, ...args) => {
        this.log(`WebSocket Event [${event}]: ${JSON.stringify(args)}`);
      });

      setTimeout(() => {
        reject(new Error('WebSocket connection timeout'));
      }, 10000);
    });
  }

  async subscribeToRideRequests() {
    this.log('Step 4: Subscribing to ride requests...');

    return new Promise((resolve, reject) => {
      this.driverSocket.emit('subscribe_to_ride_requests', {
        latitude: -15.78853,
        longitude: 35.00494,
        maxRadius: 25
      });

      this.driverSocket.on('ride_requests_subscription_confirmed', (data) => {
        this.addResult('Ride Subscription', true, `Subscribed with radius ${data.maxRadius}km`);
        resolve();
      });

      this.driverSocket.on('ride_requests_subscription_failed', (data) => {
        this.addResult('Ride Subscription', false, `Subscription failed: ${data.reason}`);
        reject(new Error(data.reason));
      });

      this.driverSocket.on('error', (error) => {
        this.addResult('Ride Subscription', false, `Subscription error: ${error.message || error}`);
        reject(error);
      });

      setTimeout(() => {
        reject(new Error('Subscription timeout'));
      }, 10000);
    });
  }

  async loginAsPassenger() {
    this.log('Step 5: Logging in as passenger...');

    try {
      const response = await axios.post(`${API_BASE}/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });

      if (response.data.token && response.data.user.role === 'passenger') {
        this.passengerToken = response.data.token;
        this.addResult('Passenger Login', true, 'Successfully logged in as passenger');
      } else {
        throw new Error('Passenger login failed or user is not a passenger');
      }
    } catch (error) {
      this.addResult('Passenger Login', false, `Passenger login failed: ${error.response?.data?.message || error.message}`);
      throw error;
    }
  }

  async testRideNotification() {
    this.log('Step 6: Testing ride notification...');

    return new Promise(async (resolve, reject) => {
      let notificationReceived = false;

      // Set up listener for ride notification
      this.driverSocket.on('new_ride_request', (rideData) => {
        notificationReceived = true;
        this.currentRide = rideData;
        this.addResult('Ride Notification', true, `Received notification for ride ${rideData.rideId}`);
        this.log(`Ride Details: ${JSON.stringify(rideData, null, 2)}`);
        resolve();
      });

      try {
        // Create ride request
        const response = await axios.post(`${API_BASE}/rides/book`, {
          pickupLocation: 'Lilongwe City Centre',
          pickupLatitude: -15.78853,
          pickupLongitude: 35.00494,
          dropoffLocation: 'Kamuzu International Airport',
          dropoffLatitude: -13.7894,
          dropoffLongitude: 33.7831,
          optionId: 'standard',
          paymentMethod: 'cash'
        }, {
          headers: { Authorization: `Bearer ${this.passengerToken}` }
        });

        if (response.data.bookingId || response.data.success || response.data.ride) {
          const rideId = response.data.bookingId || response.data.ride?.id || 'unknown';
          this.log(`Ride created with ID: ${rideId}`);
          this.log(`Full response: ${JSON.stringify(response.data, null, 2)}`);

          // Wait for notification
          setTimeout(() => {
            if (!notificationReceived) {
              this.addResult('Ride Notification', false, 'No notification received within timeout');
              reject(new Error('Notification timeout'));
            }
          }, 15000);
        } else {
          this.log(`Full response: ${JSON.stringify(response.data, null, 2)}`);
          throw new Error('Failed to create ride');
        }
      } catch (error) {
        this.addResult('Ride Creation', false, `Ride creation failed: ${error.response?.data?.message || error.message}`);
        this.log(`Full error response: ${JSON.stringify(error.response?.data, null, 2)}`);
        reject(error);
      }
    });
  }

  async testDriverResponse() {
    this.log('Step 7: Testing driver response...');

    if (!this.currentRide) {
      this.addResult('Driver Response', false, 'No ride available to respond to');
      return;
    }

    try {
      // Test accepting the ride
      const response = await axios.post(`${API_BASE}/drivers/rides/${this.currentRide.rideId}/accept`, {
        driverLatitude: -15.78853,
        driverLongitude: 35.00494,
        estimatedArrival: '8 minutes'
      }, {
        headers: { Authorization: `Bearer ${this.driverToken}` }
      });

      if (response.data.success) {
        this.addResult('Driver Accept Ride', true, `Successfully accepted ride ${this.currentRide.rideId}`);
      } else {
        throw new Error('Failed to accept ride');
      }
    } catch (error) {
      this.addResult('Driver Accept Ride', false, `Accept ride failed: ${error.response?.data?.message || error.message}`);
    }
  }

  async cleanup() {
    this.log('Step 8: Cleaning up...');

    try {
      // End driver shift
      if (this.driverToken) {
        await axios.post(`${API_BASE}/drivers/shift/end`, {
          latitude: -15.78853,
          longitude: 35.00494,
          notes: 'Ending shift after test'
        }, {
          headers: { Authorization: `Bearer ${this.driverToken}` }
        });
        this.addResult('End Shift', true, 'Successfully ended driver shift');
      }

      // Disconnect WebSocket
      if (this.driverSocket) {
        this.driverSocket.disconnect();
        this.addResult('WebSocket Cleanup', true, 'WebSocket disconnected');
      }
    } catch (error) {
      this.addResult('Cleanup', false, `Cleanup failed: ${error.response?.data?.message || error.message}`);
    }
  }

  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('='.repeat(60));

    const passed = this.testResults.filter(r => r.success).length;
    const failed = this.testResults.filter(r => !r.success).length;

    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / this.testResults.length) * 100).toFixed(1)}%\n`);

    this.testResults.forEach(result => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.testName}: ${result.message}`);
    });

    if (failed > 0) {
      console.log('\n⚠️  Some tests failed. Check the logs above for details.');
      console.log('💡 Common issues:');
      console.log('   - Make sure the server is running on port 3000');
      console.log('   - Ensure test users exist in the database');
      console.log('   - Check that vehicle ID 1 exists');
      console.log('   - Verify WebSocket gateway is properly configured');
    } else {
      console.log('\n🎉 All tests passed! The driver notification flow is working correctly.');
    }
  }
}

// Run the test
const tester = new DriverFlowTester();
tester.runCompleteTest().catch(console.error);
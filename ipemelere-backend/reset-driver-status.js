#!/usr/bin/env node

/**
 * Reset Driver Status Script
 *
 * This script resets the driver status to available for testing
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const API_BASE = `${BASE_URL}/api/v1`;

async function resetDriverStatus() {
  console.log('🔄 Resetting Driver Status...\n');

  try {
    // Login as driver
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (!loginResponse.data.token) {
      throw new Error('Login failed');
    }

    const token = loginResponse.data.token;
    console.log('✅ Driver logged in successfully');

    // Check current status
    const statusResponse = await axios.get(`${API_BASE}/drivers/status`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    console.log('📊 Current Status:', JSON.stringify(statusResponse.data, null, 2));

    // If driver is busy, try to end current ride or shift
    if (statusResponse.data.status === 'busy') {
      console.log('⚠️  Driver is busy, attempting to reset...');

      try {
        // Try to end shift (this should reset status)
        await axios.post(`${API_BASE}/drivers/shift/end`, {
          latitude: -15.78853,
          longitude: 35.00494,
          notes: 'Resetting status for testing'
        }, {
          headers: { Authorization: `Bearer ${token}` }
        });
        console.log('✅ Ended current shift');
      } catch (error) {
        console.log('⚠️  Could not end shift:', error.response?.data?.message || error.message);
      }
    }

    // Set driver to available
    try {
      await axios.put(`${API_BASE}/drivers/availability`, {
        isAvailable: true
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Set driver availability to true');
    } catch (error) {
      console.log('⚠️  Could not set availability:', error.response?.data?.message || error.message);
    }

    // Check final status
    const finalStatusResponse = await axios.get(`${API_BASE}/drivers/status`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    console.log('\n📊 Final Status:', JSON.stringify(finalStatusResponse.data, null, 2));

    if (finalStatusResponse.data.canSubscribeToRides) {
      console.log('\n🎉 Driver is now ready for WebSocket testing!');
    } else {
      console.log('\n⚠️  Driver still cannot subscribe to rides. Manual intervention may be needed.');
    }

  } catch (error) {
    console.error('❌ Error resetting driver status:', error.response?.data?.message || error.message);
  }
}

resetDriverStatus().catch(console.error);
const io = require('socket.io-client');
const jwt = require('jsonwebtoken');
const axios = require('axios');

console.log('🔔 WebSocket Driver Notification Test - FOCUSED');
console.log('===============================================\n');

// Configuration
const SERVER_URL = 'http://localhost:3001';
const JWT_SECRET = 'your-super-secret-jwt-key-change-in-production';

// Test users (using the test data we created)
const driverUser = {
  sub: 16,
  userId: 16,
  email: '<EMAIL>',
  roles: ['driver'],
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
};

const passengerUser = {
  sub: 18,
  userId: 18,
  email: '<EMAIL>',
  roles: ['passenger'],
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
};

// Generate tokens
const driverToken = jwt.sign(driverUser, JWT_SECRET);
const passengerToken = jwt.sign(passengerUser, JWT_SECRET);

console.log('🔑 Generated test tokens');
console.log(`   Driver Token: ${driverToken.substring(0, 50)}...`);
console.log(`   Passenger Token: ${passengerToken.substring(0, 50)}...\n`);

class WebSocketNotificationTest {
  constructor() {
    this.driverSocket = null;
    this.testResults = [];
    this.rideCreated = null;
    this.notificationReceived = false;
    this.notificationData = null;
    this.testStartTime = Date.now();
  }

  async runTest() {
    try {
      console.log('🚀 Starting WebSocket Driver Notification Test\n');
      
      // Step 1: Connect driver to WebSocket
      await this.connectDriver();
      
      // Step 2: Subscribe driver to ride requests
      await this.subscribeDriverToRideRequests();
      
      // Step 3: Create a ride as passenger
      await this.createRideAsPassenger();
      
      // Step 4: Wait for driver notification
      await this.waitForDriverNotification();
      
      // Step 5: Verify notification content
      this.verifyNotificationContent();
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
      this.addResult('Overall Test', false, error.message);
      this.printResults();
    } finally {
      this.cleanup();
    }
  }

  async connectDriver() {
    console.log('📡 Step 1: Connecting driver to WebSocket...');
    
    return new Promise((resolve, reject) => {
      this.driverSocket = io(`${SERVER_URL}/driver-availability`, {
        auth: { token: driverToken },
        autoConnect: true,
        timeout: 10000
      });

      this.driverSocket.on('connect', () => {
        console.log(`   ✅ Driver connected with socket ID: ${this.driverSocket.id}`);
        this.addResult('Driver WebSocket Connection', true, `Connected with ID: ${this.driverSocket.id}`);
        resolve();
      });

      this.driverSocket.on('connect_error', (error) => {
        console.log(`   ❌ Driver connection failed: ${error.message}`);
        this.addResult('Driver WebSocket Connection', false, error.message);
        reject(error);
      });

      // Set up notification listener early
      this.setupNotificationListener();

      setTimeout(() => {
        reject(new Error('Driver connection timeout'));
      }, 10000);
    });
  }

  setupNotificationListener() {
    console.log('   🎧 Setting up notification listeners...');
    
    // Listen for new ride request notification
    this.driverSocket.on('new_ride_request', (rideData) => {
      console.log('\n🎉 *** DRIVER NOTIFICATION RECEIVED! ***');
      console.log('==========================================');
      console.log(`   🆔 Ride ID: ${rideData.id}`);
      console.log(`   👤 Passenger: ${rideData.passengerName || 'N/A'}`);
      console.log(`   📍 Pickup: ${rideData.pickupAddress}`);
      console.log(`   📍 Dropoff: ${rideData.dropoffAddress}`);
      console.log(`   📏 Distance from driver: ${rideData.distanceFromPickup}km`);
      console.log(`   💰 Estimated Price: $${rideData.estimatedPrice}`);
      console.log(`   ⏰ Response deadline: ${rideData.responseDeadline}`);
      console.log('==========================================\n');
      
      this.notificationReceived = true;
      this.notificationData = rideData;
      this.addResult('Driver Notification Received', true, `Received notification for ride ${rideData.id}`);
    });

    // Listen for other events
    this.driverSocket.on('ride_expired', (data) => {
      console.log(`   ⏰ Ride ${data.rideId} expired`);
    });

    this.driverSocket.on('error', (error) => {
      console.log(`   ❌ WebSocket error: ${error.message}`);
    });

    console.log('   ✅ Notification listeners set up');
  }

  async subscribeDriverToRideRequests() {
    console.log('\n📍 Step 2: Subscribing driver to ride requests...');
    
    return new Promise((resolve, reject) => {
      // Listen for subscription confirmation
      this.driverSocket.on('ride_requests_subscription_confirmed', (data) => {
        console.log('   ✅ Driver subscription confirmed');
        console.log(`   📍 Location: ${data.location.latitude}, ${data.location.longitude}`);
        console.log(`   🎯 Max Radius: ${data.maxRadius}km`);
        console.log(`   🆔 Driver ID: ${data.driverId}`);
        this.addResult('Driver Subscription', true, `Subscribed at ${data.location.latitude}, ${data.location.longitude}`);
        resolve();
      });

      this.driverSocket.on('error', (error) => {
        console.log(`   ❌ Subscription failed: ${error.message}`);
        this.addResult('Driver Subscription', false, error.message);
        reject(new Error(error.message));
      });

      // Subscribe to ride requests at test location in Malawi
      console.log('   📤 Sending subscription request...');
      this.driverSocket.emit('subscribe_to_ride_requests', {
        latitude: -15.78853,
        longitude: 35.00494,
        maxRadius: 25
      });

      setTimeout(() => {
        reject(new Error('Subscription timeout'));
      }, 5000);
    });
  }

  async createRideAsPassenger() {
    console.log('\n🚗 Step 3: Creating ride as passenger...');
    
    try {
      const rideData = {
        pickupAddress: "Test Pickup Location - Lilongwe",
        pickupLatitude: -15.78853,
        pickupLongitude: 35.00494,
        dropoffAddress: "Test Dropoff Location - Lilongwe",
        dropoffLatitude: -15.7894,
        dropoffLongitude: 35.0831
      };

      console.log(`   📍 Pickup: ${rideData.pickupAddress}`);
      console.log(`   📍 Dropoff: ${rideData.dropoffAddress}`);
      console.log(`   📤 Sending ride creation request...`);

      const response = await axios.post(`${SERVER_URL}/api/v1/rides/book`, rideData, {
        headers: {
          'Authorization': `Bearer ${passengerToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 201) {
        this.rideCreated = response.data;
        console.log(`   ✅ Ride created successfully`);
        console.log(`   🆔 Ride ID: ${this.rideCreated.id}`);
        console.log(`   💰 Estimated Price: $${this.rideCreated.estimatedPrice}`);
        this.addResult('Ride Creation', true, `Ride ${this.rideCreated.id} created successfully`);
      } else {
        throw new Error(`Unexpected response status: ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Ride creation failed: ${error.message}`);
      if (error.response) {
        console.log(`   📄 Response status: ${error.response.status}`);
        console.log(`   📄 Response data:`, error.response.data);
      }
      this.addResult('Ride Creation', false, error.message);
      throw error;
    }
  }

  async waitForDriverNotification() {
    console.log('\n🔔 Step 4: Waiting for driver notification...');
    console.log('   ⏳ Waiting up to 20 seconds for notification...');
    
    return new Promise((resolve, reject) => {
      // Check if notification was already received
      if (this.notificationReceived) {
        console.log('   ✅ Notification already received!');
        resolve(this.notificationData);
        return;
      }

      // Set timeout for notification
      const timeout = setTimeout(() => {
        if (!this.notificationReceived) {
          console.log('   ❌ No notification received within timeout');
          this.addResult('Driver Notification Timeout', false, 'Driver did not receive ride request within 20 seconds');
          reject(new Error('Notification timeout'));
        }
      }, 20000); // 20 second timeout

      // Check periodically if notification was received
      const checkInterval = setInterval(() => {
        if (this.notificationReceived) {
          clearTimeout(timeout);
          clearInterval(checkInterval);
          console.log('   ✅ Notification received!');
          resolve(this.notificationData);
        }
      }, 500);
    });
  }

  verifyNotificationContent() {
    console.log('\n🔍 Step 5: Verifying notification content...');
    
    if (!this.notificationData) {
      this.addResult('Notification Content Verification', false, 'No notification data to verify');
      return;
    }

    const checks = [
      { name: 'Ride ID', check: () => this.notificationData.id === this.rideCreated.id },
      { name: 'Pickup Address', check: () => this.notificationData.pickupAddress },
      { name: 'Dropoff Address', check: () => this.notificationData.dropoffAddress },
      { name: 'Distance from Pickup', check: () => typeof this.notificationData.distanceFromPickup === 'number' },
      { name: 'Estimated Price', check: () => typeof this.notificationData.estimatedPrice === 'number' },
      { name: 'Response Deadline', check: () => this.notificationData.responseDeadline }
    ];

    let passedChecks = 0;
    checks.forEach(check => {
      const passed = check.check();
      console.log(`   ${passed ? '✅' : '❌'} ${check.name}: ${passed ? 'Valid' : 'Invalid'}`);
      if (passed) passedChecks++;
    });

    const allPassed = passedChecks === checks.length;
    this.addResult('Notification Content Verification', allPassed, 
      `${passedChecks}/${checks.length} content checks passed`);
  }

  addResult(testName, passed, message) {
    this.testResults.push({ testName, passed, message });
  }

  printResults() {
    const duration = ((Date.now() - this.testStartTime) / 1000).toFixed(1);
    
    console.log('\n📊 WebSocket Notification Test Results');
    console.log('======================================');
    
    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;
    const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
    
    this.testResults.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.testName}: ${result.message}`);
    });
    
    console.log(`\n📈 Test Summary:`);
    console.log(`   Total Tests: ${total}`);
    console.log(`   Passed: ${passed}`);
    console.log(`   Failed: ${total - passed}`);
    console.log(`   Success Rate: ${percentage}%`);
    console.log(`   Duration: ${duration}s\n`);
    
    if (percentage === 100) {
      console.log('🎉 ALL TESTS PASSED! 🎉');
      console.log('✅ WebSocket driver notifications are working perfectly!');
      console.log('✅ Drivers are receiving real-time ride requests!');
      console.log('✅ The notification system is fully operational!');
      console.log('✅ End-to-end WebSocket flow is working correctly!');
    } else if (percentage >= 80) {
      console.log('✅ Most tests passed! Driver notification system is mostly working');
      console.log('⚠️ Some minor issues detected - check failed tests above');
    } else {
      console.log('⚠️ Several tests failed. Driver notification system needs attention');
      console.log('❌ Critical issues detected - system may not be working properly');
    }
  }

  cleanup() {
    console.log('\n🧹 Cleaning up...');
    if (this.driverSocket) {
      this.driverSocket.disconnect();
      console.log('   ✅ Driver socket disconnected');
    }
  }
}

// Run the test
const test = new WebSocketNotificationTest();
test.runTest().then(() => {
  console.log('\n🏁 WebSocket notification test finished');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Test failed with error:', error.message);
  process.exit(1);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(0);
});

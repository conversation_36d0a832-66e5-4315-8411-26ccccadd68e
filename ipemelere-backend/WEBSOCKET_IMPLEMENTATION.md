# WebSocket Real-time Location Streaming Implementation

## Overview

This implementation replaces the polling-based location tracking with efficient WebSocket streaming for real-time driver and passenger location updates. The system provides instant location updates, reduces battery drain, and improves user experience.

## Architecture

### WebSocket Namespaces

1. **`/location`** - Handles driver location updates and passenger location subscriptions
2. **`/rides`** - Manages ride status updates and notifications

### Core Components

#### Services
- **`WebSocketAuthService`** - Handles JWT authentication for WebSocket connections
- **`LocationStreamingService`** - Processes and broadcasts driver location updates
- **`RideRoomService`** - Manages ride-based rooms for isolated communication
- **`PassengerSubscriptionService`** - Manages passenger subscriptions to driver location updates
- **`WebSocketIntegrationService`** - Integrates WebSocket functionality with existing ride management
- **`ConnectionStateService`** - Manages connection states, heartbeats, and connection monitoring
- **`WebSocketFallbackService`** - Provides fallback REST API functionality when WebSocket is unavailable

#### Gateways
- **`LocationGateway`** - WebSocket gateway for location-related events
- **`RideGateway`** - WebSocket gateway for ride status and notifications

## WebSocket Events

### Location Namespace (`/location`)

#### Client → Server Events

**`update_location`** - Driver sends location update
```typescript
{
  rideId?: number;
  latitude: number;
  longitude: number;
  speed?: number;
  heading?: number;
  accuracy?: number;
  isMovingToPickup?: boolean;
}
```

**`join_ride_room`** - Join a ride room for location updates
```typescript
{
  rideId: number;
}
```

**`leave_ride_room`** - Leave a ride room
```typescript
{
  rideId: number;
}
```

**`request_driver_location`** - Passenger requests current driver location
```typescript
{
  rideId: number;
}
```

**`subscribe_driver_tracking`** - Passenger subscribes to driver tracking
```typescript
{
  rideId: number;
}
```

**`unsubscribe_driver_tracking`** - Passenger unsubscribes from driver tracking
```typescript
// No body required
```

**`get_room_status`** - Get room connection status
```typescript
{
  rideId: number;
}
```

**`ping_room`** - Ping room for connectivity check
```typescript
{
  rideId: number;
}
```

**`heartbeat_response`** - Response to server heartbeat
```typescript
// No payload required - just acknowledgment
```

**`connection_status`** - Request connection status information
```typescript
// No payload required
```

**`reconnect_attempt`** - Notify server of reconnection attempt
```typescript
{
  previousConnectionId?: string;
  reason?: string;
}
```

#### Server → Client Events

**`connection_established`** - Connection confirmation
```typescript
{
  message: string;
  userId: number;
  userRole: string;
  timestamp: Date;
  connectionId: string;
}
```

**`driver_location_update`** - Real-time driver location broadcast
```typescript
{
  driverId: number;
  rideId: number;
  location: {
    latitude: number;
    longitude: number;
    timestamp: Date;
    speed?: number;
    heading?: number;
    accuracy?: number;
  };
  movement: {
    isMoving: boolean;
    isMovingToPickup: boolean;
    distanceFromPickup?: number;
    estimatedArrival?: string;
  };
  driver: {
    name: string;
    rating: number;
    vehicleInfo?: {
      make: string;
      model: string;
      plateNumber: string;
      color: string;
    };
  };
}
```

**`location_update_confirmed`** - Confirmation of location update
```typescript
{
  message: string;
  timestamp: Date;
  rideId?: number;
}
```

**`ride_room_joined`** - Confirmation of joining ride room
```typescript
{
  message: string;
  rideId: number;
  timestamp: Date;
}
```

**`user_joined_room`** - Notification when another user joins the room
```typescript
{
  userId: number;
  userType: 'driver' | 'passenger';
  timestamp: Date;
}
```

**`user_left_room`** - Notification when a user leaves the room
```typescript
{
  userId: number;
  timestamp: Date;
}
```

**`ride_room_left`** - Room leave confirmation
```typescript
{
  message: string;
  rideId: number;
  timestamp: Date;
}
```

**`driver_location_unavailable`** - Driver location not available
```typescript
{
  message: string;
  rideId: number;
  timestamp: Date;
}
```

**`driver_tracking_subscribed`** - Driver tracking subscription confirmation
```typescript
{
  message: string;
  rideId: number;
  driverInfo: any;
  timestamp: Date;
}
```

**`driver_tracking_unsubscribed`** - Driver tracking unsubscription confirmation
```typescript
{
  message: string;
  timestamp: Date;
}
```

**`room_status`** - Room status information
```typescript
{
  rideId: number;
  connectionCount: number;
  timestamp: Date;
}
```

**`room_pong`** - Room ping response
```typescript
{
  rideId: number;
  roomName: string;
  timestamp: Date;
  message: string;
}
```

**`room_joined_successfully`** - Detailed room join confirmation
```typescript
{
  rideId: number;
  roomName: string;
  userType: 'driver' | 'passenger';
  timestamp: Date;
}
```

**`heartbeat`** - Server heartbeat to check connection
```typescript
{
  timestamp: Date;
  serverTime: number;
}
```

**`connection_status_response`** - Connection status information
```typescript
{
  connectionState: {
    connectionId: string;
    userId: number;
    userRole: string;
    isActive: boolean;
    connectedAt: Date;
    lastActivity: Date;
    reconnectAttempts: number;
    lastReconnectAt?: Date;
    metadata: any;
  };
  serverMetrics: {
    activeConnections: number;
    driversConnected: number;
    passengersConnected: number;
  };
  timestamp: Date;
}
```

**`reconnect_acknowledged`** - Reconnection acknowledgment
```typescript
{
  message: string;
  connectionId: string;
  reconnectAttempts: number;
  timestamp: Date;
}
```

**`error`** - Error messages
```typescript
{
  message: string;
  code: string;
  details?: any;
}
```

### Driver Availability Namespace (`/driver-availability`)

#### Client → Server Events

**`subscribe_to_ride_requests`** - Driver subscribes to nearby ride requests
```typescript
{
  latitude: number;
  longitude: number;
  maxRadius?: number; // Optional: max radius in km (default: 25km)
}
```

**`update_location_for_requests`** - Driver updates location for ride matching
```typescript
{
  latitude: number;
  longitude: number;
}
```

**`unsubscribe_from_ride_requests`** - Driver unsubscribes from ride requests
```typescript
// No payload required
```

**`get_subscription_status`** - Get current subscription status
```typescript
// No payload required
```

**`accept_ride`** - Accept a ride request via WebSocket
```typescript
{
  rideId: number;
  driverLatitude: number;
  driverLongitude: number;
  estimatedArrival?: number; // Optional: estimated arrival time in minutes
}
```

**`reject_ride`** - Reject a ride request via WebSocket
```typescript
{
  rideId: number;
  reason?: string; // Optional: reason for rejection
}
```

#### Server → Client Events

**`connection_established`** - Connection confirmation
```typescript
{
  message: string;
  userId: number;
  driverId: number;
  timestamp: Date;
  connectionId: string;
}
```

**`ride_requests_subscription_confirmed`** - Subscription confirmation
```typescript
{
  message: string;
  driverId: number;
  location: {
    latitude: number;
    longitude: number;
  };
  maxRadius: number;
  timestamp: Date;
}
```

**`location_update_confirmed`** - Location update confirmation
```typescript
{
  message: string;
  location: {
    latitude: number;
    longitude: number;
  };
  timestamp: Date;
}
```

**`new_ride_request`** - Real-time ride request notification
```typescript
{
  rideId: number;
  pickupAddress: string;
  pickupLatitude: number;
  pickupLongitude: number;
  dropoffAddress: string;
  dropoffLatitude: number;
  dropoffLongitude: number;
  estimatedPrice: number;
  distance: number;
  distanceFromDriver: number; // Distance from driver to pickup in km
  estimatedDuration: number;
  passengerName: string;
  passengerRating?: number;
  requestedAt: Date;
  responseDeadline: Date; // When this request expires
}
```

**`ride_request_expired`** - Ride request no longer available
```typescript
{
  rideId: number;
  message: string;
  timestamp: Date;
}
```

**`ride_requests_unsubscribed`** - Unsubscription confirmation
```typescript
{
  message: string;
  driverId: number;
  timestamp: Date;
}
```

**`subscription_status`** - Current subscription status
```typescript
{
  isSubscribed: boolean;
  location?: {
    latitude: number;
    longitude: number;
  };
  maxRadius?: number;
  subscribedAt?: Date;
  lastLocationUpdate?: Date;
  timestamp: Date;
}
```

**`ride_accepted`** - Confirmation of ride acceptance
```typescript
{
  success: boolean;
  message: string;
  rideId: number;
  rideDetails: {
    rideId: number;
    status: string;
    passenger: object;
    pickup: object;
    dropoff: object;
  };
  timestamp: Date;
}
```

**`ride_rejected`** - Confirmation of ride rejection
```typescript
{
  success: boolean;
  message: string;
  rideId: number;
  reason?: string;
  timestamp: Date;
}
```

**`error`** - Error messages
```typescript
{
  message: string;
  code: string;
  details?: any;
}
```

### Rides Namespace (`/rides`)

#### Client → Server Events

**`join_ride_notifications`** - Subscribe to ride notifications
```typescript
{
  rideId: number;
}
```

**`leave_ride_notifications`** - Unsubscribe from ride notifications
```typescript
{
  rideId: number;
}
```

#### Server → Client Events

**`connection_established`** - Connection confirmation
```typescript
{
  message: string;
  userId: number;
  userRole: string;
  timestamp: Date;
}
```

**`ride_notifications_joined`** - Ride notifications subscription confirmation
```typescript
{
  message: string;
  rideId: number;
  timestamp: Date;
}
```

**`ride_notifications_left`** - Ride notifications unsubscription confirmation
```typescript
{
  message: string;
  rideId: number;
  timestamp: Date;
}
```

**`ride_status_updated`** - Ride status change notification
```typescript
{
  rideId: number;
  status: RideStatus;
  message?: string;
  estimatedArrival?: string;
  additionalData?: any;
  timestamp: Date;
}
```

**`ride_notification`** - General ride notifications
```typescript
{
  rideId: number;
  type: 'driver_assigned' | 'driver_arrived' | 'ride_started' | 'ride_completed' | 'ride_cancelled';
  message: string;
  data?: any;
  timestamp: Date;
}
```

**`error`** - Error messages
```typescript
{
  message: string;
  code: string;
  rideId?: number;
}
```

## Authentication

WebSocket connections require JWT authentication. Tokens can be provided via:

1. **Handshake auth**: `socket.handshake.auth.token`
2. **Query parameters**: `?token=JWT_TOKEN`
3. **Authorization header**: `Authorization: Bearer JWT_TOKEN`

## Room Management

### Ride Rooms
- Each active ride has its own room: `ride_{rideId}`
- Only authorized participants (driver and passenger) can join
- Rooms are automatically cleaned up when rides complete

### Authorization Rules
- **Drivers**: Can send location updates, join their assigned ride rooms
- **Passengers**: Can subscribe to their ride's location updates, receive notifications

## Usage Examples

### Driver Location Updates

```javascript
// Connect to location namespace
const locationSocket = io('/location', {
  auth: { token: 'JWT_TOKEN' }
});

// Join ride room
locationSocket.emit('join_ride_room', { rideId: 123 });

// Send location updates
setInterval(() => {
  locationSocket.emit('update_location', {
    rideId: 123,
    latitude: 6.5244,
    longitude: 3.3792,
    speed: 25,
    isMovingToPickup: true
  });
}, 5000); // Every 5 seconds
```

### Passenger Location Subscription

```javascript
// Connect to location namespace
const locationSocket = io('/location', {
  auth: { token: 'JWT_TOKEN' }
});

// Join ride room to receive driver location updates
locationSocket.emit('join_ride_room', { rideId: 123 });

// Listen for driver location updates
locationSocket.on('driver_location_update', (data) => {
  console.log('Driver location:', data.location);
  console.log('ETA:', data.movement.estimatedArrival);
  // Update map with driver location
});
```

### Ride Notifications

```javascript
// Connect to rides namespace
const ridesSocket = io('/rides', {
  auth: { token: 'JWT_TOKEN' }
});

// Subscribe to ride notifications
ridesSocket.emit('join_ride_notifications', { rideId: 123 });

// Listen for ride status updates
ridesSocket.on('ride_status_updated', (data) => {
  console.log('Ride status:', data.status);
});

// Listen for ride notifications
ridesSocket.on('ride_notification', (data) => {
  console.log('Notification:', data.message);
});
```

## Benefits

1. **Real-time Updates**: Instant location and status updates
2. **Reduced Battery Drain**: No need for frequent HTTP polling
3. **Lower Network Overhead**: Persistent connections vs multiple HTTP requests
4. **Better User Experience**: Smooth, responsive tracking
5. **Scalable**: Efficient handling of multiple concurrent rides

## Backward Compatibility

The existing REST API endpoints remain functional:
- `PUT /api/v1/drivers/location` - Driver location updates
- `GET /api/v1/rides/:id/driver-status` - Get driver status

This allows gradual migration from polling to WebSocket-based real-time updates.

## Error Handling

- **Authentication failures**: Connection automatically disconnected
- **Invalid data**: Error events sent to client
- **Network issues**: Automatic reconnection handling on client side
- **Room authorization**: Access denied for unauthorized users

## Connection State Management

### Heartbeat System
- **Interval**: 30 seconds between heartbeats
- **Timeout**: 10 seconds for heartbeat response
- **Max Missed**: 3 missed heartbeats before disconnection
- **Auto-reconnection**: Client-side reconnection with exponential backoff

### Connection Monitoring
- Real-time connection state tracking
- User activity monitoring
- Connection duration statistics
- Automatic cleanup of stale connections

### Fallback Mechanisms
When WebSocket connections are unavailable or unreliable:

#### REST API Fallback Endpoints
- `POST /api/v1/websocket-fallback/driver/location` - Driver location updates
- `GET /api/v1/websocket-fallback/passenger/driver-location/:rideId` - Get driver location
- `GET /api/v1/websocket-fallback/ride-status/:rideId` - Get ride status updates
- `GET /api/v1/websocket-fallback/connection-check` - Check WebSocket availability

#### Adaptive Polling
- Dynamic poll intervals based on activity (5-30 seconds)
- Automatic fallback detection
- Smart reconnection attempts

## Administrative Endpoints

### WebSocket Monitoring (`/api/v1/websocket-admin/`)
- `GET /rooms/statistics` - Room and connection statistics
- `GET /rooms/active` - List all active rooms
- `GET /rooms/:rideId/details` - Detailed room information
- `GET /rooms/:rideId/activities` - Room activity history
- `GET /health` - System health status
- `GET /performance/metrics` - Performance metrics

### Connection Management
- `GET /connection-stats` - Detailed connection statistics
- `POST /force-disconnect` - Force disconnect user connections
- `GET /debug/cleanup` - Manual cleanup trigger

## Performance Considerations

- **Connection limits**: Monitor concurrent WebSocket connections (alerts at 1000+)
- **Memory usage**: Regular cleanup of completed ride rooms every 30 minutes
- **Message frequency**: Heartbeat every 30 seconds, location updates as needed
- **Scaling**: Redis adapter ready for multi-server deployments
- **Fallback**: Automatic REST API fallback when WebSocket unavailable

## Complete WebSocket Channel Reference

### Frontend Integration Guide

This section provides a comprehensive reference for all WebSocket channels that the frontend needs to listen to and interact with.

#### 1. Location Tracking Namespace (`/location`)

**Purpose**: Real-time driver location tracking and ride room management

**Frontend Connection**:
```javascript
const locationSocket = io('/location', {
  auth: { token: userJwtToken },
  transports: ['websocket', 'polling']
});
```

**Essential Events for Frontend**:

**For Drivers**:
- **Send**: `update_location` - Continuously send location updates
- **Send**: `join_ride_room` - Join ride room when ride starts
- **Send**: `leave_ride_room` - Leave room when ride ends
- **Listen**: `location_update_confirmed` - Confirm location was received
- **Listen**: `connection_established` - Connection confirmation
- **Listen**: `heartbeat` - Respond to server heartbeat
- **Send**: `heartbeat_response` - Acknowledge heartbeat

**For Passengers**:
- **Send**: `join_ride_room` - Join ride room to track driver
- **Send**: `subscribe_driver_tracking` - Subscribe to driver location
- **Send**: `request_driver_location` - Get current driver location
- **Send**: `unsubscribe_driver_tracking` - Stop tracking driver
- **Listen**: `driver_location_update` - Real-time driver location
- **Listen**: `driver_tracking_subscribed` - Tracking subscription confirmed
- **Listen**: `driver_location_unavailable` - Driver location not available
- **Listen**: `connection_established` - Connection confirmation

**For Both**:
- **Listen**: `user_joined_room` - Another user joined the ride room
- **Listen**: `user_left_room` - Another user left the ride room
- **Listen**: `room_joined_successfully` - Detailed room join confirmation
- **Listen**: `error` - Handle WebSocket errors
- **Send**: `connection_status` - Request connection status
- **Listen**: `connection_status_response` - Connection status information
- **Send**: `reconnect_attempt` - Notify server of reconnection
- **Listen**: `reconnect_acknowledged` - Reconnection confirmed

#### 2. Driver Availability Namespace (`/driver-availability`)

**Purpose**: Real-time ride request notifications for drivers

**Frontend Connection**:
```javascript
const driverAvailabilitySocket = io('/driver-availability', {
  auth: { token: userJwtToken },
  transports: ['websocket', 'polling']
});
```

**Essential Events for Frontend**:

**For Drivers Only**:
- **Send**: `subscribe_to_ride_requests` - Subscribe to nearby ride requests with location
- **Send**: `update_location_for_requests` - Update location for better matching
- **Send**: `unsubscribe_from_ride_requests` - Stop receiving ride requests
- **Send**: `get_subscription_status` - Check current subscription status
- **Listen**: `new_ride_request` - Real-time ride request notifications
- **Listen**: `ride_request_expired` - Ride no longer available
- **Listen**: `ride_requests_subscription_confirmed` - Subscription confirmed
- **Listen**: `location_update_confirmed` - Location update confirmed
- **Listen**: `subscription_status` - Current subscription status
- **Listen**: `connection_established` - Connection confirmation
- **Listen**: `error` - Handle WebSocket errors

#### 3. Ride Notifications Namespace (`/rides`)

**Purpose**: Ride status updates and notifications

**Frontend Connection**:
```javascript
const rideSocket = io('/rides', {
  auth: { token: userJwtToken },
  transports: ['websocket', 'polling']
});
```

**Essential Events for Frontend**:

**For All Users**:
- **Send**: `join_ride_notifications` - Subscribe to ride notifications
- **Send**: `leave_ride_notifications` - Unsubscribe from notifications
- **Listen**: `ride_notifications_joined` - Subscription confirmed
- **Listen**: `ride_notifications_left` - Unsubscription confirmed
- **Listen**: `ride_status_updated` - Ride status changes
- **Listen**: `ride_notification` - General ride notifications
- **Listen**: `connection_established` - Connection confirmation
- **Listen**: `error` - Handle WebSocket errors

**Specific Notification Types**:
- `driver_assigned` - Driver has been assigned to ride
- `driver_arrived` - Driver has arrived at pickup location
- `ride_started` - Ride has started
- `ride_completed` - Ride has been completed
- `ride_cancelled` - Ride has been cancelled

#### 4. Connection Management

**Heartbeat System**:
```javascript
// Listen for server heartbeat
socket.on('heartbeat', (data) => {
  // Respond immediately to maintain connection
  socket.emit('heartbeat_response');
});
```

**Connection Status Monitoring**:
```javascript
// Request connection status
socket.emit('connection_status');

// Listen for status response
socket.on('connection_status_response', (data) => {
  console.log('Connection state:', data.connectionState);
  console.log('Server metrics:', data.serverMetrics);
});
```

**Reconnection Handling**:
```javascript
socket.on('disconnect', (reason) => {
  console.log('Disconnected:', reason);
  // Attempt reconnection
  socket.emit('reconnect_attempt', {
    previousConnectionId: socket.id,
    reason: reason
  });
});

socket.on('reconnect_acknowledged', (data) => {
  console.log('Reconnection successful:', data);
});
```

#### 4. Error Handling

**Universal Error Handler**:
```javascript
socket.on('error', (error) => {
  console.error('WebSocket error:', error);

  switch(error.code) {
    case 'UNAUTHORIZED':
      // Handle authentication errors
      break;
    case 'SUBSCRIPTION_FAILED':
      // Handle subscription errors
      break;
    case 'LOCATION_REQUEST_ERROR':
      // Handle location request errors
      break;
    default:
      // Handle general errors
      break;
  }
});
```

#### 5. Room Management

**Joining Ride Rooms**:
```javascript
// For location tracking
locationSocket.emit('join_ride_room', { rideId: currentRideId });

// For ride notifications
rideSocket.emit('join_ride_notifications', { rideId: currentRideId });
```

**Leaving Ride Rooms**:
```javascript
// Always clean up when leaving a ride
locationSocket.emit('leave_ride_room', { rideId: currentRideId });
rideSocket.emit('leave_ride_notifications', { rideId: currentRideId });
```

#### 6. Fallback Mechanisms

When WebSocket connections fail, use REST API fallbacks:

```javascript
// Check WebSocket availability
const checkWebSocket = async () => {
  try {
    const response = await fetch('/api/v1/websocket-fallback/connection-check');
    const data = await response.json();
    return data.available;
  } catch (error) {
    return false;
  }
};

// Fallback for driver location (passengers)
const getDriverLocationFallback = async (rideId) => {
  const response = await fetch(`/api/v1/websocket-fallback/passenger/driver-location/${rideId}`);
  return response.json();
};

// Fallback for ride status
const getRideStatusFallback = async (rideId) => {
  const response = await fetch(`/api/v1/websocket-fallback/ride-status/${rideId}`);
  return response.json();
};
```

### Implementation Checklist for Frontend

**Essential Connections**:
- [ ] Connect to `/location` namespace for location tracking
- [ ] Connect to `/driver-availability` namespace for ride request notifications (drivers only)
- [ ] Connect to `/rides` namespace for ride notifications
- [ ] Implement JWT authentication for all connections

**Driver Implementation**:
- [ ] Send location updates via `update_location`
- [ ] Join ride rooms when ride starts
- [ ] Handle location update confirmations
- [ ] Respond to heartbeat messages
- [ ] Subscribe to ride requests via `subscribe_to_ride_requests`
- [ ] Listen for `new_ride_request` notifications
- [ ] Handle `ride_request_expired` events
- [ ] Update location for ride matching via `update_location_for_requests`

**Passenger Implementation**:
- [ ] Subscribe to driver location tracking
- [ ] Listen for real-time driver location updates
- [ ] Handle driver location unavailable scenarios
- [ ] Join ride notification rooms

**Universal Implementation**:
- [ ] Handle connection establishment events
- [ ] Implement error handling for all error codes
- [ ] Handle reconnection scenarios
- [ ] Implement fallback mechanisms for poor connectivity
- [ ] Clean up connections when leaving rides
- [ ] Monitor connection status

**Testing Scenarios**:
- [ ] Test with poor network connectivity
- [ ] Test reconnection after network loss
- [ ] Test fallback API usage
- [ ] Test multiple concurrent rides
- [ ] Test driver and passenger interactions
- [ ] Test error scenarios and recovery

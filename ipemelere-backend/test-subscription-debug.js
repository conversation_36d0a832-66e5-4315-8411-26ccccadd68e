const io = require('socket.io-client');
const jwt = require('jsonwebtoken');

console.log('🔍 WebSocket Subscription Debug Test');
console.log('====================================\n');

// Configuration
const SERVER_URL = 'http://localhost:3001';
const JWT_SECRET = 'your-super-secret-jwt-key-change-in-production';

// Test driver user
const driverUser = {
  sub: 16,
  userId: 16,
  email: '<EMAIL>',
  roles: ['driver'],
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
};

// Generate token
const driverToken = jwt.sign(driverUser, JWT_SECRET);

console.log('🔑 Generated test token');
console.log(`   Driver Token: ${driverToken.substring(0, 50)}...\n`);

class SubscriptionDebugTest {
  constructor() {
    this.driverSocket = null;
    this.connected = false;
    this.subscriptionSent = false;
    this.subscriptionConfirmed = false;
    this.errorReceived = null;
  }

  async runTest() {
    try {
      console.log('🚀 Starting Subscription Debug Test\n');
      
      await this.connectDriver();
      await this.attemptSubscription();
      await this.waitAndAnalyze();
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
      this.printResults();
    } finally {
      this.cleanup();
    }
  }

  async connectDriver() {
    console.log('📡 Step 1: Connecting driver to WebSocket...');
    
    return new Promise((resolve, reject) => {
      this.driverSocket = io(`${SERVER_URL}/driver-availability`, {
        auth: { token: driverToken },
        autoConnect: true,
        timeout: 10000
      });

      this.driverSocket.on('connect', () => {
        console.log(`   ✅ Driver connected with socket ID: ${this.driverSocket.id}`);
        this.connected = true;
        this.setupEventListeners();
        resolve();
      });

      this.driverSocket.on('connect_error', (error) => {
        console.log(`   ❌ Driver connection failed: ${error.message}`);
        reject(error);
      });

      setTimeout(() => {
        reject(new Error('Driver connection timeout'));
      }, 10000);
    });
  }

  setupEventListeners() {
    console.log('   🎧 Setting up event listeners...');
    
    // Listen for subscription confirmation
    this.driverSocket.on('ride_requests_subscription_confirmed', (data) => {
      console.log('\n🎉 *** SUBSCRIPTION CONFIRMED! ***');
      console.log('==================================');
      console.log(`   📍 Location: ${data.location.latitude}, ${data.location.longitude}`);
      console.log(`   🎯 Max Radius: ${data.maxRadius}km`);
      console.log(`   🆔 Driver ID: ${data.driverId}`);
      console.log('==================================\n');
      this.subscriptionConfirmed = true;
    });

    // Listen for errors
    this.driverSocket.on('error', (error) => {
      console.log('\n❌ *** ERROR RECEIVED! ***');
      console.log('==========================');
      console.log(`   Message: ${error.message || error}`);
      console.log(`   Code: ${error.code || 'N/A'}`);
      console.log('==========================\n');
      this.errorReceived = error;
    });

    // Listen for disconnect
    this.driverSocket.on('disconnect', (reason) => {
      console.log(`   ⚠️ Driver disconnected: ${reason}`);
    });

    // Listen for any other events
    this.driverSocket.onAny((eventName, ...args) => {
      if (!['connect', 'disconnect', 'error', 'ride_requests_subscription_confirmed'].includes(eventName)) {
        console.log(`   📨 Received event: ${eventName}`, args);
      }
    });

    console.log('   ✅ Event listeners set up');
  }

  async attemptSubscription() {
    console.log('\n📍 Step 2: Attempting subscription...');
    
    return new Promise((resolve) => {
      const subscriptionData = {
        latitude: -15.78853,
        longitude: 35.00494,
        maxRadius: 25
      };

      console.log('   📤 Sending subscription message...');
      console.log(`   📍 Location: ${subscriptionData.latitude}, ${subscriptionData.longitude}`);
      console.log(`   🎯 Max Radius: ${subscriptionData.maxRadius}km`);
      
      // Send subscription request
      this.driverSocket.emit('subscribe_to_ride_requests', subscriptionData);
      this.subscriptionSent = true;
      console.log('   ✅ Subscription message sent');
      
      // Wait a bit for response
      setTimeout(() => {
        resolve();
      }, 2000);
    });
  }

  async waitAndAnalyze() {
    console.log('\n⏱️ Step 3: Waiting for response...');
    
    return new Promise((resolve) => {
      let secondsWaited = 0;
      const maxWait = 10;
      
      const checkInterval = setInterval(() => {
        secondsWaited++;
        console.log(`   ⏳ Waiting... ${secondsWaited}/${maxWait}s`);
        
        if (this.subscriptionConfirmed || this.errorReceived || secondsWaited >= maxWait) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 1000);
    });
  }

  printResults() {
    console.log('\n📊 Subscription Debug Results');
    console.log('=============================');
    
    console.log(`✅ Connection Status: ${this.connected ? 'Connected' : 'Failed'}`);
    console.log(`📤 Subscription Sent: ${this.subscriptionSent ? 'Yes' : 'No'}`);
    console.log(`✅ Subscription Confirmed: ${this.subscriptionConfirmed ? 'Yes' : 'No'}`);
    console.log(`❌ Error Received: ${this.errorReceived ? 'Yes' : 'No'}`);
    
    if (this.errorReceived) {
      console.log(`   Error Message: ${this.errorReceived.message || this.errorReceived}`);
      console.log(`   Error Code: ${this.errorReceived.code || 'N/A'}`);
    }
    
    console.log('\n🔍 Analysis:');
    
    if (!this.connected) {
      console.log('❌ WebSocket connection failed - check server and authentication');
    } else if (!this.subscriptionSent) {
      console.log('❌ Subscription message was not sent - client-side issue');
    } else if (this.errorReceived) {
      console.log('❌ Subscription failed with error - server-side issue');
      console.log('🔧 Check server logs for more details');
      
      if (this.errorReceived.message === 'Driver ID not found') {
        console.log('🎯 Specific Issue: Driver ID lookup is failing');
        console.log('   - Check if driver exists in database');
        console.log('   - Check if WebSocket authentication is setting driverId correctly');
        console.log('   - Check if getDriverId method is working properly');
      }
    } else if (this.subscriptionConfirmed) {
      console.log('✅ Subscription working perfectly!');
      console.log('🎉 Driver notification system is operational!');
    } else {
      console.log('⚠️ No response received - possible timeout or server issue');
      console.log('🔧 Check server logs to see if subscription message was received');
    }
  }

  cleanup() {
    console.log('\n🧹 Cleaning up...');
    if (this.driverSocket) {
      this.driverSocket.disconnect();
      console.log('   ✅ Driver socket disconnected');
    }
  }
}

// Run the test
const test = new SubscriptionDebugTest();
test.runTest().then(() => {
  console.log('\n🏁 Subscription debug test finished');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Test failed with error:', error.message);
  process.exit(1);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(0);
});

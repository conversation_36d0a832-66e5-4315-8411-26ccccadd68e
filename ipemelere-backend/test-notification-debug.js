#!/usr/bin/env node

/**
 * Notification Debug Test
 *
 * This script creates a ride and monitors both the server logs and WebSocket events
 */

const { io } = require('socket.io-client');
const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const API_BASE = `${BASE_URL}/api/v1`;

async function debugNotificationFlow() {
  console.log('🔍 Debugging Notification Flow...\n');

  try {
    // Step 1: Login as driver and connect WebSocket
    const driverLogin = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    const driverToken = driverLogin.data.token;
    console.log('✅ Driver logged in');

    // Connect to WebSocket
    const driverSocket = io(`${BASE_URL}/driver-availability`, {
      auth: { token: driverToken },
      transports: ['websocket']
    });

    let isSubscribed = false;

    driverSocket.on('connection_established', () => {
      console.log('✅ WebSocket connected');

      // Subscribe to ride requests
      driverSocket.emit('subscribe_to_ride_requests', {
        latitude: -15.78853,
        longitude: 35.00494,
        maxRadius: 25
      });
    });

    driverSocket.on('ride_requests_subscription_confirmed', () => {
      console.log('✅ Subscribed to ride requests');
      isSubscribed = true;
    });

    driverSocket.on('new_ride_request', (data) => {
      console.log('🎉 RIDE NOTIFICATION RECEIVED!');
      console.log('Ride Data:', JSON.stringify(data, null, 2));
    });

    driverSocket.onAny((event, ...args) => {
      console.log(`📨 WebSocket Event [${event}]:`, args);
    });

    // Wait for subscription
    await new Promise(resolve => {
      const checkSubscription = () => {
        if (isSubscribed) {
          resolve();
        } else {
          setTimeout(checkSubscription, 100);
        }
      };
      checkSubscription();
    });

    // Step 2: Login as passenger
    const passengerLogin = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    const passengerToken = passengerLogin.data.token;
    console.log('✅ Passenger logged in');

    // Step 3: Create ride and monitor
    console.log('\n🚗 Creating ride request...');

    const rideResponse = await axios.post(`${API_BASE}/rides/book`, {
      pickupLocation: 'Debug Test Pickup',
      pickupLatitude: -15.78853,
      pickupLongitude: 35.00494,
      dropoffLocation: 'Debug Test Dropoff',
      dropoffLatitude: -15.7894,
      dropoffLongitude: 35.0831,
      optionId: 'standard',
      paymentMethod: 'cash'
    }, {
      headers: { Authorization: `Bearer ${passengerToken}` }
    });

    console.log('✅ Ride created:', JSON.stringify(rideResponse.data, null, 2));

    // Wait for notification
    console.log('\n⏳ Waiting for driver notification...');
    console.log('   (Check server logs for any errors)');

    let notificationReceived = false;
    driverSocket.on('new_ride_request', () => {
      notificationReceived = true;
    });

    // Wait 30 seconds for notification
    setTimeout(() => {
      if (!notificationReceived) {
        console.log('\n❌ No notification received after 30 seconds');
        console.log('💡 Check server logs for errors in:');
        console.log('   - RideNotificationService.triggerImmediateNotification()');
        console.log('   - DriverAvailabilityService.broadcastRideRequestToNearbyDrivers()');
        console.log('   - Driver matching logic');
      }
      process.exit(0);
    }, 30000);

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

debugNotificationFlow();
const io = require('socket.io-client');
const jwt = require('jsonwebtoken');

console.log('🔌 WebSocket Connection Test - BASIC');
console.log('====================================\n');

// Configuration
const SERVER_URL = 'http://localhost:3001';
const JWT_SECRET = 'your-super-secret-jwt-key-change-in-production';

// Test driver user
const driverUser = {
  sub: 16,
  userId: 16,
  email: '<EMAIL>',
  roles: ['driver'],
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
};

// Generate token
const driverToken = jwt.sign(driverUser, JWT_SECRET);

console.log('🔑 Generated test token');
console.log(`   Driver Token: ${driverToken.substring(0, 50)}...\n`);

class BasicWebSocketTest {
  constructor() {
    this.driverSocket = null;
    this.testResults = [];
    this.testStartTime = Date.now();
    this.subscriptionConfirmed = false;
  }

  async runTest() {
    try {
      console.log('🚀 Starting Basic WebSocket Connection Test\n');
      
      // Step 1: Connect driver to WebSocket
      await this.connectDriver();
      
      // Step 2: Try to subscribe (but don't fail if it doesn't work)
      await this.attemptSubscription();
      
      // Step 3: Keep connection alive for a bit
      await this.keepConnectionAlive();
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
      this.addResult('Overall Test', false, error.message);
      this.printResults();
    } finally {
      this.cleanup();
    }
  }

  async connectDriver() {
    console.log('📡 Step 1: Connecting driver to WebSocket...');
    
    return new Promise((resolve, reject) => {
      this.driverSocket = io(`${SERVER_URL}/driver-availability`, {
        auth: { token: driverToken },
        autoConnect: true,
        timeout: 10000
      });

      this.driverSocket.on('connect', () => {
        console.log(`   ✅ Driver connected with socket ID: ${this.driverSocket.id}`);
        this.addResult('Driver WebSocket Connection', true, `Connected with ID: ${this.driverSocket.id}`);
        resolve();
      });

      this.driverSocket.on('connect_error', (error) => {
        console.log(`   ❌ Driver connection failed: ${error.message}`);
        this.addResult('Driver WebSocket Connection', false, error.message);
        reject(error);
      });

      this.driverSocket.on('disconnect', (reason) => {
        console.log(`   ⚠️ Driver disconnected: ${reason}`);
      });

      this.driverSocket.on('error', (error) => {
        console.log(`   ❌ WebSocket error: ${error.message || error}`);
      });

      setTimeout(() => {
        reject(new Error('Driver connection timeout'));
      }, 10000);
    });
  }

  async attemptSubscription() {
    console.log('\n📍 Step 2: Attempting driver subscription...');
    
    return new Promise((resolve) => {
      // Listen for subscription confirmation
      this.driverSocket.on('ride_requests_subscription_confirmed', (data) => {
        console.log('   ✅ Driver subscription confirmed!');
        console.log(`   📍 Location: ${data.location.latitude}, ${data.location.longitude}`);
        console.log(`   🎯 Max Radius: ${data.maxRadius}km`);
        console.log(`   🆔 Driver ID: ${data.driverId}`);
        this.subscriptionConfirmed = true;
        this.addResult('Driver Subscription', true, `Successfully subscribed - Driver ID: ${data.driverId}`);
        resolve();
      });

      this.driverSocket.on('error', (error) => {
        console.log(`   ⚠️ Subscription error: ${error.message || error}`);
        this.addResult('Driver Subscription', false, error.message || error);
        resolve(); // Don't fail the test, just note the error
      });

      // Try to subscribe
      console.log('   📤 Sending subscription request...');
      this.driverSocket.emit('subscribe_to_ride_requests', {
        latitude: -15.78853,
        longitude: 35.00494,
        maxRadius: 25
      });

      // Don't wait too long for subscription
      setTimeout(() => {
        if (!this.subscriptionConfirmed) {
          console.log('   ⏰ Subscription attempt timed out (this is OK for basic test)');
          this.addResult('Driver Subscription', false, 'Subscription timeout - but connection is working');
        }
        resolve();
      }, 5000);
    });
  }

  async keepConnectionAlive() {
    console.log('\n⏱️ Step 3: Keeping connection alive for 5 seconds...');
    
    return new Promise((resolve) => {
      let secondsLeft = 5;
      const interval = setInterval(() => {
        console.log(`   ⏳ Connection alive... ${secondsLeft}s remaining`);
        secondsLeft--;
        
        if (secondsLeft <= 0) {
          clearInterval(interval);
          console.log('   ✅ Connection remained stable');
          this.addResult('Connection Stability', true, 'Connection remained stable for 5 seconds');
          resolve();
        }
      }, 1000);
    });
  }

  addResult(testName, passed, message) {
    this.testResults.push({ testName, passed, message });
  }

  printResults() {
    const duration = ((Date.now() - this.testStartTime) / 1000).toFixed(1);
    
    console.log('\n📊 Basic WebSocket Test Results');
    console.log('===============================');
    
    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;
    const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
    
    this.testResults.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.testName}: ${result.message}`);
    });
    
    console.log(`\n📈 Test Summary:`);
    console.log(`   Total Tests: ${total}`);
    console.log(`   Passed: ${passed}`);
    console.log(`   Failed: ${total - passed}`);
    console.log(`   Success Rate: ${percentage}%`);
    console.log(`   Duration: ${duration}s\n`);
    
    // Analyze results
    const connectionWorking = this.testResults.find(r => r.testName === 'Driver WebSocket Connection')?.passed;
    const subscriptionWorking = this.testResults.find(r => r.testName === 'Driver Subscription')?.passed;
    const connectionStable = this.testResults.find(r => r.testName === 'Connection Stability')?.passed;
    
    console.log('🔍 Analysis:');
    
    if (connectionWorking) {
      console.log('✅ WebSocket connection is working - drivers can connect');
      
      if (subscriptionWorking) {
        console.log('✅ Driver subscription is working - drivers can subscribe to ride requests');
        console.log('✅ The WebSocket driver notification system is FULLY OPERATIONAL!');
        console.log('🎉 Drivers will receive real-time ride request notifications!');
      } else {
        console.log('⚠️ Driver subscription has issues - but connection works');
        console.log('🔧 The WebSocket server is working, subscription logic needs debugging');
      }
      
      if (connectionStable) {
        console.log('✅ Connection is stable - no disconnection issues');
      } else {
        console.log('⚠️ Connection stability issues detected');
      }
    } else {
      console.log('❌ WebSocket connection is not working - drivers cannot connect');
      console.log('🔧 Need to fix basic WebSocket connection issues first');
    }
  }

  cleanup() {
    console.log('\n🧹 Cleaning up...');
    if (this.driverSocket) {
      this.driverSocket.disconnect();
      console.log('   ✅ Driver socket disconnected');
    }
  }
}

// Run the test
const test = new BasicWebSocketTest();
test.runTest().then(() => {
  console.log('\n🏁 Basic WebSocket test finished');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Test failed with error:', error.message);
  process.exit(1);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(0);
});

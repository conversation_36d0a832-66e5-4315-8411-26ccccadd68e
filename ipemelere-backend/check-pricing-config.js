const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkAndUpdatePricingConfig() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE
  });

  try {
    console.log('Connected to MySQL database');
    
    // Check current active pricing configuration
    console.log('\n=== Current Active Pricing Configuration ===');
    const [rows] = await connection.execute(
      'SELECT * FROM pricing_configurations WHERE isActive = 1'
    );
    
    if (rows.length > 0) {
      const config = rows[0];
      console.log(`ID: ${config.id}`);
      console.log(`Name: ${config.name}`);
      console.log(`Model: ${config.model}`);
      console.log(`Base Price: ${config.basePrice} MWK`);
      console.log(`Price per KM: ${config.pricePerKm} MWK`);
      console.log(`Description: ${config.description}`);
      
      // Check if we need to update to the new pricing
      if (config.pricePerKm != 3000 || config.model !== 'distance_only') {
        console.log('\n=== Updating to New Pricing ===');
        
        await connection.execute(`
          UPDATE pricing_configurations 
          SET 
            model = 'distance_only',
            basePrice = 3000.00,
            pricePerKm = 3000.00,
            vehicleTypePricing = NULL,
            description = 'Distance-only pricing: 3000 MWK base + 3000 MWK per km'
          WHERE isActive = 1
        `);
        
        console.log('✅ Pricing configuration updated successfully!');
        
        // Verify the update
        const [updatedRows] = await connection.execute(
          'SELECT * FROM pricing_configurations WHERE isActive = 1'
        );
        const updatedConfig = updatedRows[0];
        console.log('\n=== Updated Configuration ===');
        console.log(`Model: ${updatedConfig.model}`);
        console.log(`Base Price: ${updatedConfig.basePrice} MWK`);
        console.log(`Price per KM: ${updatedConfig.pricePerKm} MWK`);
        console.log(`Description: ${updatedConfig.description}`);
      } else {
        console.log('\n✅ Pricing configuration is already up to date!');
      }
    } else {
      console.log('No active pricing configuration found. Creating new one...');
      
      await connection.execute(`
        INSERT INTO pricing_configurations (
          name, isActive, model, basePrice, pricePerKm, 
          vehicleTypePricing, description, priority
        ) VALUES (
          'default', 1, 'distance_only', 3000.00, 3000.00,
          NULL, 'Distance-only pricing: 3000 MWK base + 3000 MWK per km', 1
        )
      `);
      
      console.log('✅ New pricing configuration created!');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await connection.end();
  }
}

checkAndUpdatePricingConfig();

const io = require('socket.io-client');
const jwt = require('jsonwebtoken');
const axios = require('axios');

// Configuration
const SERVER_URL = 'http://localhost:3001';
const JWT_SECRET = 'your-super-secret-jwt-key-change-in-production';

// Test data
const DRIVER_USER_ID = 16;
const DRIVER_ID = 4;
const PASSENGER_USER_ID = 18;

class CompleteRideAcceptanceFlowTest {
  constructor() {
    this.testStartTime = Date.now();
    this.driverSocket = null;
    this.passengerSocket = null;
    
    // Test state tracking
    this.driverConnected = false;
    this.driverSubscribed = false;
    this.passengerConnected = false;
    this.passengerSubscribed = false;
    this.rideCreated = null;
    this.notificationReceived = false;
    this.notificationData = null;
    this.rideAccepted = false;
    this.rideAcceptanceData = null;
    this.passengerNotified = false;
    this.passengerNotificationData = null;
  }

  generateTestTokens() {
    console.log('🔑 Generated test tokens');
    
    const driverToken = jwt.sign(
      { sub: DRIVER_USER_ID, roles: ['driver'] },
      JWT_SECRET,
      { expiresIn: '1h' }
    );

    const passengerToken = jwt.sign(
      { sub: PASSENGER_USER_ID, roles: ['passenger'] },
      JWT_SECRET,
      { expiresIn: '1h' }
    );
    
    console.log(`   Driver Token: ${driverToken.substring(0, 50)}...`);
    console.log(`   Passenger Token: ${passengerToken.substring(0, 50)}...`);
    
    return { driverToken, passengerToken };
  }

  async connectDriver(driverToken) {
    console.log('\n🔌 Step 1: Connecting driver...');
    
    return new Promise((resolve, reject) => {
      this.driverSocket = io(`${SERVER_URL}/driver-availability`, {
        auth: { token: driverToken },
        autoConnect: true,
        timeout: 10000,
        forceNew: true
      });

      this.driverSocket.on('connect', () => {
        console.log(`   ✅ Driver connected: ${this.driverSocket.id}`);
        this.driverConnected = true;
        this.setupDriverEventListeners();
        
        // Wait for authentication to complete
        setTimeout(() => {
          resolve();
        }, 1000);
      });

      this.driverSocket.on('connect_error', (error) => {
        console.log(`   ❌ Driver connection failed: ${error.message}`);
        reject(error);
      });

      this.driverSocket.on('disconnect', (reason) => {
        console.log(`   ⚠️ Driver disconnected: ${reason}`);
        this.driverConnected = false;
      });
    });
  }

  async connectPassenger(passengerToken) {
    console.log('\n🔌 Step 2: Connecting passenger...');
    
    return new Promise((resolve, reject) => {
      this.passengerSocket = io(`${SERVER_URL}/rides`, {
        auth: { token: passengerToken },
        autoConnect: true,
        timeout: 10000,
        forceNew: true
      });

      this.passengerSocket.on('connect', () => {
        console.log(`   ✅ Passenger connected: ${this.passengerSocket.id}`);
        this.passengerConnected = true;
        this.setupPassengerEventListeners();
        
        // Wait for authentication to complete
        setTimeout(() => {
          resolve();
        }, 1000);
      });

      this.passengerSocket.on('connect_error', (error) => {
        console.log(`   ❌ Passenger connection failed: ${error.message}`);
        reject(error);
      });

      this.passengerSocket.on('disconnect', (reason) => {
        console.log(`   ⚠️ Passenger disconnected: ${reason}`);
        this.passengerConnected = false;
      });
    });
  }

  setupDriverEventListeners() {
    console.log('   🎧 Setting up driver event listeners...');
    
    // Listen for ride request notifications
    this.driverSocket.on('new_ride_request', (rideData) => {
      console.log('\n🎉 *** DRIVER: RIDE REQUEST NOTIFICATION RECEIVED! ***');
      console.log('=============================================');
      console.log(`   🆔 Ride ID: ${rideData.rideId}`);
      console.log(`   👤 Passenger: ${rideData.passengerName || 'N/A'}`);
      console.log(`   📍 Pickup: ${rideData.pickupAddress}`);
      console.log(`   📍 Dropoff: ${rideData.dropoffAddress}`);
      console.log(`   📏 Distance: ${rideData.distanceFromDriver}km`);
      console.log(`   💰 Price: $${rideData.estimatedPrice}`);
      console.log(`   ⏰ Deadline: ${rideData.responseDeadline}`);
      console.log('=============================================\n');
      
      this.notificationReceived = true;
      this.notificationData = rideData;
      
      // Auto-accept the ride after 2 seconds
      setTimeout(() => {
        this.acceptRide(rideData.rideId);
      }, 2000);
    });

    // Listen for ride acceptance confirmation
    this.driverSocket.on('ride_accepted', (data) => {
      console.log('\n🎉 *** DRIVER: RIDE ACCEPTANCE CONFIRMED! ***');
      console.log('=============================================');
      console.log(`   ✅ Success: ${data.success}`);
      console.log(`   📝 Message: ${data.message}`);
      console.log(`   🆔 Ride ID: ${data.rideId}`);
      console.log(`   📊 Ride Details: ${JSON.stringify(data.rideDetails, null, 2)}`);
      console.log('=============================================\n');
      
      this.rideAccepted = true;
      this.rideAcceptanceData = data;
    });

    // Listen for subscription confirmation
    this.driverSocket.on('ride_requests_subscription_confirmed', (data) => {
      console.log(`   ✅ Driver subscription confirmed (Driver ID: ${data.driverId})`);
      this.driverSubscribed = true;
    });

    // Listen for connection established
    this.driverSocket.on('connection_established', (data) => {
      console.log(`   📡 Driver connection established (Driver ID: ${data.driverId})`);
    });

    // Listen for errors
    this.driverSocket.on('error', (error) => {
      console.log(`   ❌ Driver Error: ${error.message || error}`);
    });

    console.log('   ✅ Driver event listeners set up');
  }

  setupPassengerEventListeners() {
    console.log('   🎧 Setting up passenger event listeners...');
    
    // Listen for driver assignment notifications
    this.passengerSocket.on('ride_notification', (data) => {
      if (data.type === 'driver_assigned') {
        console.log('\n🎉 *** PASSENGER: DRIVER ASSIGNED NOTIFICATION! ***');
        console.log('=============================================');
        console.log(`   🆔 Ride ID: ${data.rideId}`);
        console.log(`   📝 Message: ${data.message}`);
        console.log(`   🚗 Driver: ${data.data?.name || 'N/A'}`);
        console.log(`   ⭐ Rating: ${data.data?.rating || 'N/A'}`);
        console.log(`   📱 Phone: ${data.data?.phone || 'N/A'}`);
        console.log(`   🚙 Vehicle: ${data.data?.vehicle || 'N/A'}`);
        console.log(`   ⏰ ETA: ${data.data?.estimatedArrival || 'N/A'} minutes`);
        console.log('=============================================\n');

        this.passengerNotified = true;
        this.passengerNotificationData = data;
      }
    });

    // Listen for ride status updates
    this.passengerSocket.on('ride_status_updated', (data) => {
      console.log(`   📊 Passenger: Ride status updated to ${data.status} for ride ${data.rideId}`);
    });

    // Listen for subscription confirmation
    this.passengerSocket.on('ride_notifications_joined', (data) => {
      console.log(`   ✅ Passenger subscribed to ride ${data.rideId} notifications`);
      this.passengerSubscribed = true;
    });

    // Listen for connection established
    this.passengerSocket.on('connection_established', (data) => {
      console.log(`   📡 Passenger connection established (User ID: ${data.userId})`);
    });

    // Listen for errors
    this.passengerSocket.on('error', (error) => {
      console.log(`   ❌ Passenger Error: ${error.message || error}`);
    });

    console.log('   ✅ Passenger event listeners set up');
  }

  async subscribeDriver() {
    console.log('\n📍 Step 3: Subscribing driver to ride requests...');
    
    return new Promise((resolve, reject) => {
      if (!this.driverConnected) {
        reject(new Error('Driver not connected'));
        return;
      }

      console.log('   📤 Sending driver subscription request...');
      this.driverSocket.emit('subscribe_to_ride_requests', {
        latitude: -15.78853,
        longitude: 35.00494,
        maxRadius: 25
      });

      const timeout = setTimeout(() => {
        if (!this.driverSubscribed) {
          console.log('   ⚠️ Driver subscription timeout, but continuing...');
        }
        resolve();
      }, 5000);

      const checkInterval = setInterval(() => {
        if (this.driverSubscribed) {
          clearTimeout(timeout);
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
    });
  }

  async subscribePassengerToRide(rideId) {
    console.log('\n📍 Step 4: Subscribing passenger to ride notifications...');

    return new Promise((resolve, reject) => {
      if (!this.passengerConnected) {
        reject(new Error('Passenger not connected'));
        return;
      }

      console.log(`   📤 Subscribing passenger to ride ${rideId} notifications...`);
      this.passengerSocket.emit('join_ride_notifications', {
        rideId: rideId
      });

      const timeout = setTimeout(() => {
        if (!this.passengerSubscribed) {
          console.log('   ⚠️ Passenger subscription timeout, but continuing...');
        }
        resolve();
      }, 5000);

      const checkInterval = setInterval(() => {
        if (this.passengerSubscribed) {
          clearTimeout(timeout);
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
    });
  }

  async createRide(passengerToken) {
    console.log('\n🚗 Step 5: Creating ride...');
    
    try {
      const response = await axios.post(`${SERVER_URL}/api/v1/rides/book`, {
        optionId: 'sedan_standard',
        pickupLocation: 'ACCEPTANCE TEST Pickup - Lilongwe City Center',
        pickupLatitude: -15.78853,
        pickupLongitude: 35.00494,
        dropoffLocation: 'ACCEPTANCE TEST Dropoff - Lilongwe Mall',
        dropoffLatitude: -15.7894,
        dropoffLongitude: 35.0831,
        paymentMethod: 'cash'
      }, {
        headers: {
          'Authorization': `Bearer ${passengerToken}`,
          'Content-Type': 'application/json'
        }
      });

      this.rideCreated = response.data;
      console.log('   ✅ Ride created successfully!');
      console.log(`   🆔 Booking ID: ${this.rideCreated.bookingId}`);
      console.log(`   📊 Status: ${this.rideCreated.status}`);
      
      return this.rideCreated;
    } catch (error) {
      console.log('   ❌ Failed to create ride:', error.response?.data || error.message);
      throw error;
    }
  }

  acceptRide(rideId) {
    console.log('\n🔵 Step 6: Driver accepting ride...');
    console.log(`   📤 Sending ride acceptance for ride ${rideId}...`);
    
    this.driverSocket.emit('accept_ride', {
      rideId: rideId,
      driverLatitude: -15.78853,
      driverLongitude: 35.00494,
      estimatedArrival: 5 // 5 minutes
    });
  }

  async waitForRideAcceptance() {
    console.log('\n⏳ Step 7: Waiting for ride acceptance and passenger notification...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        if (!this.rideAccepted) {
          console.log('   ❌ Ride acceptance timeout');
          reject(new Error('Ride acceptance timeout'));
        } else if (!this.passengerNotified) {
          console.log('   ⚠️ Passenger notification timeout, but ride was accepted');
          resolve();
        }
      }, 15000);

      const checkInterval = setInterval(() => {
        if (this.rideAccepted && this.passengerNotified) {
          clearTimeout(timeout);
          clearInterval(checkInterval);
          console.log('   ✅ Ride accepted and passenger notified!');
          resolve();
        } else if (this.rideAccepted) {
          console.log('   ⏳ Ride accepted, waiting for passenger notification...');
        }
      }, 1000);
    });
  }

  printResults() {
    const duration = ((Date.now() - this.testStartTime) / 1000).toFixed(1);
    
    console.log('\n📊 COMPLETE Ride Acceptance Flow Results');
    console.log('==========================================');
    
    console.log(`✅ Driver Connected: ${this.driverConnected ? 'Yes' : 'No'}`);
    console.log(`📍 Driver Subscribed: ${this.driverSubscribed ? 'Yes' : 'No'}`);
    console.log(`✅ Passenger Connected: ${this.passengerConnected ? 'Yes' : 'No'}`);
    console.log(`🚗 Ride Created: ${this.rideCreated ? 'Yes' : 'No'}`);
    console.log(`🔔 Driver Notification: ${this.notificationReceived ? 'Yes' : 'No'}`);
    console.log(`🎯 Ride Accepted: ${this.rideAccepted ? 'Yes' : 'No'}`);
    console.log(`📢 Passenger Notified: ${this.passengerNotified ? 'Yes' : 'No'}`);
    
    if (this.rideCreated) {
      console.log(`   Booking ID: ${this.rideCreated.bookingId}`);
      console.log(`   Status: ${this.rideCreated.status}`);
    }
    
    if (this.rideAcceptanceData) {
      console.log(`   Accepted Ride ID: ${this.rideAcceptanceData.rideId}`);
      console.log(`   Acceptance Success: ${this.rideAcceptanceData.success}`);
    }
    
    console.log(`\n⏱️ Test Duration: ${duration}s`);
    
    console.log('\n🎯 FINAL VERDICT:');
    
    if (this.rideAccepted && this.passengerNotified && this.notificationReceived && this.rideCreated) {
      console.log('🎉 *** COMPLETE SUCCESS! ***');
      console.log('✅ Complete ride acceptance flow is FULLY WORKING!');
      console.log('✅ Driver receives ride notifications via WebSocket!');
      console.log('✅ Driver can accept rides via WebSocket!');
      console.log('✅ Passenger receives driver assignment notifications!');
      console.log('✅ End-to-end ride acceptance flow is operational!');
      console.log('✅ The system is ready for production use!');
      console.log('✅ MISSION ACCOMPLISHED! 🚀');
      console.log('\n🔥 PROOF: Complete ride acceptance flow working via WebSocket!');
    } else {
      console.log('❌ System has issues that need to be addressed:');
      if (!this.driverConnected) console.log('   ❌ Driver connection failed');
      if (!this.driverSubscribed) console.log('   ❌ Driver subscription failed');
      if (!this.passengerConnected) console.log('   ❌ Passenger connection failed');
      if (!this.rideCreated) console.log('   ❌ Ride creation failed');
      if (!this.notificationReceived) console.log('   ❌ Driver notification failed');
      if (!this.rideAccepted) console.log('   ❌ Ride acceptance failed');
      if (!this.passengerNotified) console.log('   ❌ Passenger notification failed');
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    if (this.driverSocket) {
      this.driverSocket.disconnect();
      console.log('   ✅ Driver socket disconnected');
    }
    
    if (this.passengerSocket) {
      this.passengerSocket.disconnect();
      console.log('   ✅ Passenger socket disconnected');
    }
  }

  async run() {
    try {
      console.log('🎯 COMPLETE Ride Acceptance Flow Test');
      console.log('=====================================\n');

      const { driverToken, passengerToken } = this.generateTestTokens();

      console.log('\n🚀 Starting COMPLETE Ride Acceptance Flow Test');

      // Step 1: Connect driver
      await this.connectDriver(driverToken);

      // Step 2: Connect passenger  
      await this.connectPassenger(passengerToken);

      // Step 3: Subscribe driver to ride requests
      await this.subscribeDriver();

      // Step 4: Create ride
      const ride = await this.createRide(passengerToken);

      // Step 5: Subscribe passenger to ride notifications
      await this.subscribePassengerToRide(ride.bookingId);

      // Step 6 & 7: Wait for notification, acceptance, and passenger notification
      await this.waitForRideAcceptance();

      // Print results
      this.printResults();

    } catch (error) {
      console.error('\n❌ Test failed:', error.message);
      this.printResults();
    } finally {
      await this.cleanup();
      console.log('\n🏁 Complete ride acceptance flow test finished\n');
    }
  }
}

// Run the test
const test = new CompleteRideAcceptanceFlowTest();
test.run().catch(console.error);

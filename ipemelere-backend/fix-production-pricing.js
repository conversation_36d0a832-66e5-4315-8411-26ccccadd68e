#!/usr/bin/env node

/**
 * Production Pricing Fix Script
 * Run this on production to fix pricing configuration issues
 */

require('dotenv').config();
const mysql = require('mysql2/promise');

async function fixProductionPricing() {
  let connection;
  
  try {
    console.log('🔧 Production Pricing Fix Script');
    console.log('=================================\n');

    // Connect to database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
    });

    console.log('✅ Connected to production database\n');

    // 1. Check current pricing configuration
    const [configRows] = await connection.execute(`
      SELECT id, name, isActive, model, basePrice, pricePerKm, description
      FROM pricing_configurations 
      WHERE isActive = 1
      ORDER BY priority DESC, createdAt DESC
      LIMIT 1
    `);

    if (configRows.length === 0) {
      console.log('❌ No active pricing configuration found. Creating one...');
      
      await connection.execute(`
        INSERT INTO pricing_configurations (
          name, isActive, model, basePrice, pricePerKm, 
          vehicleTypePricing, description, priority
        ) VALUES (
          'production_fixed', 1, 'distance_only', 3000.00, 3000.00,
          NULL, 'Production fix: 3000 MWK base + 3000 MWK per km', 1
        )
      `);
      
      console.log('✅ Created new active pricing configuration');
    } else {
      const config = configRows[0];
      console.log('Current active configuration:');
      console.log(`  ID: ${config.id}`);
      console.log(`  Name: ${config.name}`);
      console.log(`  Base Price: ${config.basePrice}`);
      console.log(`  Price per KM: ${config.pricePerKm}`);

      // Check if values are valid
      const basePrice = Number(config.basePrice);
      const pricePerKm = Number(config.pricePerKm);

      if (isNaN(basePrice) || isNaN(pricePerKm) || basePrice <= 0 || pricePerKm <= 0) {
        console.log('❌ Invalid pricing values detected. Fixing...');
        
        await connection.execute(`
          UPDATE pricing_configurations 
          SET 
            basePrice = 3000.00,
            pricePerKm = 3000.00,
            model = 'distance_only',
            vehicleTypePricing = NULL,
            description = 'Production fix: 3000 MWK base + 3000 MWK per km'
          WHERE id = ?
        `, [config.id]);
        
        console.log('✅ Fixed pricing configuration');
      } else {
        console.log('✅ Pricing configuration looks valid');
      }
    }

    // 2. Ensure only one active configuration
    console.log('\n2. Ensuring single active configuration...');
    
    const [activeConfigs] = await connection.execute(`
      SELECT id, name FROM pricing_configurations WHERE isActive = 1
    `);

    if (activeConfigs.length > 1) {
      console.log(`Found ${activeConfigs.length} active configurations. Fixing...`);
      
      // Deactivate all
      await connection.execute(`UPDATE pricing_configurations SET isActive = 0`);
      
      // Activate the latest one
      await connection.execute(`
        UPDATE pricing_configurations 
        SET isActive = 1 
        WHERE id = (
          SELECT id FROM (
            SELECT id FROM pricing_configurations 
            ORDER BY priority DESC, createdAt DESC 
            LIMIT 1
          ) as latest
        )
      `);
      
      console.log('✅ Fixed multiple active configurations');
    } else {
      console.log('✅ Single active configuration confirmed');
    }

    // 3. Test the fix with a sample calculation
    console.log('\n3. Testing price calculation...');
    
    const [testConfig] = await connection.execute(`
      SELECT basePrice, pricePerKm FROM pricing_configurations 
      WHERE isActive = 1 LIMIT 1
    `);

    if (testConfig.length > 0) {
      const config = testConfig[0];
      const basePrice = Number(config.basePrice);
      const pricePerKm = Number(config.pricePerKm);
      const testDistance = 10; // 10km test
      const totalPrice = basePrice + (testDistance * pricePerKm);

      console.log(`Test calculation for ${testDistance}km:`);
      console.log(`  Base Price: ${basePrice} MWK`);
      console.log(`  Distance Price: ${testDistance * pricePerKm} MWK`);
      console.log(`  Total Price: ${totalPrice} MWK`);

      if (isNaN(totalPrice)) {
        console.log('❌ Test calculation still produces NaN!');
      } else {
        console.log('✅ Test calculation successful');
      }
    }

    // 4. Show final configuration
    console.log('\n4. Final pricing configuration:');
    const [finalConfig] = await connection.execute(`
      SELECT * FROM pricing_configurations WHERE isActive = 1 LIMIT 1
    `);

    if (finalConfig.length > 0) {
      const config = finalConfig[0];
      console.log(`  ID: ${config.id}`);
      console.log(`  Name: ${config.name}`);
      console.log(`  Model: ${config.model}`);
      console.log(`  Base Price: ${config.basePrice} MWK`);
      console.log(`  Price per KM: ${config.pricePerKm} MWK`);
      console.log(`  Description: ${config.description}`);
    }

    console.log('\n✅ Production pricing fix completed!');
    console.log('\n🔄 Please restart your application server (pm2 restart) to apply changes.');

  } catch (error) {
    console.error('❌ Error during fix:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the fix script
fixProductionPricing().catch(console.error);

import { MigrationInterface, QueryRunner } from "typeorm";

export class CreatePricingConfigurationTable1752520000000 implements MigrationInterface {
    name = 'CreatePricingConfigurationTable1752520000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create pricing_configurations table
        await queryRunner.query(`
            CREATE TABLE \`pricing_configurations\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`recordStatus\` enum('Active', 'Deleted') NOT NULL DEFAULT 'Active',
                \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                \`deletedAt\` timestamp(6) NULL,
                \`name\` varchar(255) NOT NULL,
                \`isActive\` tinyint NOT NULL DEFAULT 1,
                \`model\` enum('vehicle_type_based', 'distance_only', 'hybrid') NOT NULL DEFAULT 'distance_only',
                \`basePrice\` decimal(10,2) NOT NULL DEFAULT '3000.00',
                \`pricePerKm\` decimal(10,2) NOT NULL DEFAULT '500.00',
                \`vehicleTypePricing\` json NULL,
                \`additionalFactors\` json NULL,
                \`validFrom\` timestamp NULL,
                \`validTo\` timestamp NULL,
                \`description\` text NULL,
                \`priority\` int NOT NULL DEFAULT '0',
                UNIQUE INDEX \`IDX_pricing_configurations_name\` (\`name\`),
                INDEX \`IDX_pricing_configurations_active\` (\`isActive\`),
                INDEX \`IDX_pricing_configurations_valid_dates\` (\`validFrom\`, \`validTo\`),
                INDEX \`IDX_pricing_configurations_priority\` (\`priority\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB
        `);

        // Insert default pricing configuration with Malawian Kwacha values
        await queryRunner.query(`
            INSERT INTO \`pricing_configurations\` (
                \`name\`,
                \`isActive\`,
                \`model\`,
                \`basePrice\`,
                \`pricePerKm\`,
                \`vehicleTypePricing\`,
                \`description\`,
                \`priority\`
            ) VALUES (
                'default',
                1,
                'distance_only',
                3000.00,
                3000.00,
                'Distance-only pricing: 3000 MWK base + 3000 MWK per km',
                1
            )
        `);

        // Insert fallback distance-only configuration
        await queryRunner.query(`
            INSERT INTO \`pricing_configurations\` (
                \`name\`, 
                \`isActive\`, 
                \`model\`, 
                \`basePrice\`, 
                \`pricePerKm\`, 
                \`description\`,
                \`priority\`
            ) VALUES (
                'fallback_distance_only',
                0,
                'distance_only',
                3000.00,
                3000.00,
                'Fallback configuration: 3000 MWK base + 3000 MWK per km',
                0
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE \`pricing_configurations\``);
    }
}
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RideCancellationService } from './ride-cancellation.service';
import { Ride, RideStatus } from '../entities/ride.entity';
import { RideCancellation } from '../entities/ride-cancellation.entity';
import { User, UserRole } from '../../users/entities/user.entity';
import { Driver, DriverStatus } from '../../drivers/entities/driver.entity';
import { CancellationPenaltyService, PenaltyResult } from './cancellation-penalty.service';
import { DriverBehaviorService, DriverBehaviorScore } from '../../drivers/services/driver-behavior.service';
import { DriverMovementService } from '../../drivers/services/driver-movement.service';
import { DriverStatusService } from '../../drivers/services/driver-status.service';
import { WebSocketIntegrationService } from '../../websockets/services/websocket-integration.service';

describe('RideCancellationService', () => {
  let service: RideCancellationService;
  let rideRepository: jest.Mocked<Repository<Ride>>;
  let cancellationRepository: jest.Mocked<Repository<RideCancellation>>;
  let userRepository: jest.Mocked<Repository<User>>;
  let cancellationPenaltyService: jest.Mocked<CancellationPenaltyService>;
  let driverBehaviorService: jest.Mocked<DriverBehaviorService>;
  let driverStatusService: jest.Mocked<DriverStatusService>;
  let webSocketIntegrationService: jest.Mocked<WebSocketIntegrationService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RideCancellationService,
        {
          provide: getRepositoryToken(Ride),
          useValue: {
            findOne: jest.fn(),
            update: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(RideCancellation),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Driver),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: CancellationPenaltyService,
          useValue: {
            calculatePenalty: jest.fn(),
          },
        },
        {
          provide: DriverBehaviorService,
          useValue: {
            trackDriverCancellation: jest.fn(),
            calculateBehaviorScore: jest.fn(),
            isDriverEligible: jest.fn(),
          },
        },
        {
          provide: DriverMovementService,
          useValue: {
            validateMovementFromInitial: jest.fn(),
          },
        },
        {
          provide: DriverStatusService,
          useValue: {
            updateDriverStatus: jest.fn(),
          },
        },
        {
          provide: WebSocketIntegrationService,
          useValue: {
            broadcastRideCancelled: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<RideCancellationService>(RideCancellationService);
    rideRepository = module.get(getRepositoryToken(Ride));
    cancellationRepository = module.get(getRepositoryToken(RideCancellation));
    userRepository = module.get(getRepositoryToken(User));
    cancellationPenaltyService = module.get(CancellationPenaltyService);
    driverBehaviorService = module.get(DriverBehaviorService);
    driverStatusService = module.get(DriverStatusService);
    webSocketIntegrationService = module.get(WebSocketIntegrationService);
  });

  describe('cancelRide', () => {
    const mockRide = {
      id: 1,
      passengerId: 1,
      driverId: 2,
      status: RideStatus.ACCEPTED,
      passenger: { id: 1, firstName: 'John', lastName: 'Doe' },
      driver: { id: 2, userId: 3 },
    } as Ride;

    const mockPassenger = {
      id: 1,
      role: UserRole.PASSENGER,
    } as User;

    const mockDriver = {
      id: 2, // This should match the driverId in mockRide
      role: UserRole.DRIVER,
    } as User;

    const mockPenaltyResult: PenaltyResult = {
      shouldApply: false,
      penaltyAmount: 0,
      reason: 'No penalty applied',
      distanceFromPickup: 0,
      distanceFromInitial: 0,
      movementDetected: false,
    };

    const createMockBehaviorScore = (score: number): DriverBehaviorScore => ({
      score,
      totalRides: 100,
      totalCancellations: 5,
      cancellationRate: 0.05,
      acceptanceRate: 0.9,
      averageResponseTime: 30,
      rejectionRate: 0.1,
      riskLevel: score > 70 ? 'low' : score > 50 ? 'medium' : score > 30 ? 'high' : 'critical',
    });

    beforeEach(() => {
      rideRepository.findOne.mockResolvedValue(mockRide);
      cancellationRepository.create.mockReturnValue({} as RideCancellation);
      cancellationRepository.save.mockResolvedValue({ id: 1 } as RideCancellation);
      cancellationPenaltyService.calculatePenalty.mockResolvedValue(mockPenaltyResult);
      rideRepository.update.mockResolvedValue({} as any);
      driverStatusService.updateDriverStatus.mockResolvedValue({
        isValid: true,
        errors: [],
        warnings: [],
        currentState: {
          status: DriverStatus.AVAILABLE,
          isOnShift: true,
          canReceiveRides: true,
        },
      });
      webSocketIntegrationService.broadcastRideCancelled.mockResolvedValue();
    });

    it('should send WebSocket notification to driver when passenger cancels', async () => {
      // Arrange
      userRepository.findOne.mockResolvedValue(mockPassenger);

      // Act
      await service.cancelRide(1, 1, 'Changed my mind');

      // Assert
      expect(webSocketIntegrationService.broadcastRideCancelled).toHaveBeenCalledWith(
        1,
        'passenger',
        'Changed my mind',
        {
          penaltyApplied: false,
          penaltyAmount: 0,
        },
      );
    });

    it('should update driver status to available when passenger cancels', async () => {
      // Arrange
      userRepository.findOne.mockResolvedValue(mockPassenger);

      // Act
      await service.cancelRide(1, 1, 'Changed my mind');

      // Assert
      expect(driverStatusService.updateDriverStatus).toHaveBeenCalledWith(
        2, // driverId
        { status: DriverStatus.AVAILABLE },
        'ride_cancelled_by_passenger',
      );
    });

    it('should update driver status to available when driver cancels (not suspended)', async () => {
      // Arrange
      userRepository.findOne.mockResolvedValue(mockDriver);
      driverBehaviorService.trackDriverCancellation.mockResolvedValue();
      driverBehaviorService.calculateBehaviorScore.mockResolvedValue(createMockBehaviorScore(85));
      driverBehaviorService.isDriverEligible.mockResolvedValue(true);

      // Act
      await service.cancelRide(1, 2, 'Emergency');

      // Assert
      expect(driverStatusService.updateDriverStatus).toHaveBeenCalledWith(
        2, // driverId
        { status: DriverStatus.AVAILABLE },
        'ride_cancelled_by_driver',
      );
    });

    it('should not update driver status when driver cancels and gets suspended', async () => {
      // Arrange
      userRepository.findOne.mockResolvedValue(mockDriver);
      driverBehaviorService.trackDriverCancellation.mockResolvedValue();
      driverBehaviorService.calculateBehaviorScore.mockResolvedValue(createMockBehaviorScore(30));
      driverBehaviorService.isDriverEligible.mockResolvedValue(false);

      // Act
      await service.cancelRide(1, 3, 'Emergency');

      // Assert
      expect(driverStatusService.updateDriverStatus).not.toHaveBeenCalledWith(
        2,
        { status: DriverStatus.AVAILABLE },
        'ride_cancelled_by_driver',
      );
    });

    it('should not send WebSocket notification when driver cancels', async () => {
      // Arrange
      userRepository.findOne.mockResolvedValue(mockDriver);
      driverBehaviorService.trackDriverCancellation.mockResolvedValue();
      driverBehaviorService.calculateBehaviorScore.mockResolvedValue(createMockBehaviorScore(85));
      driverBehaviorService.isDriverEligible.mockResolvedValue(true);

      // Act
      await service.cancelRide(1, 3, 'Emergency');

      // Assert
      expect(webSocketIntegrationService.broadcastRideCancelled).not.toHaveBeenCalled();
    });

    it('should handle WebSocket notification failure gracefully', async () => {
      // Arrange
      userRepository.findOne.mockResolvedValue(mockPassenger);
      webSocketIntegrationService.broadcastRideCancelled.mockRejectedValue(new Error('WebSocket error'));

      // Act & Assert
      await expect(service.cancelRide(1, 1, 'Changed my mind')).resolves.not.toThrow();
      expect(driverStatusService.updateDriverStatus).toHaveBeenCalled();
    });

    it('should handle driver status update failure gracefully', async () => {
      // Arrange
      userRepository.findOne.mockResolvedValue(mockPassenger);
      driverStatusService.updateDriverStatus.mockRejectedValue(new Error('Status update error'));

      // Act & Assert
      await expect(service.cancelRide(1, 1, 'Changed my mind')).resolves.not.toThrow();
      expect(webSocketIntegrationService.broadcastRideCancelled).toHaveBeenCalled();
    });
  });
});

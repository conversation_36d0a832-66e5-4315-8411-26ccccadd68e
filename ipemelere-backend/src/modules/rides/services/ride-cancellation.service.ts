import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Ride, RideStatus } from '../entities/ride.entity';
import {
  RideCancellation,
  CancellationType,
} from '../entities/ride-cancellation.entity';
import { User, UserRole } from '../../users/entities/user.entity';
import { Driver, DriverStatus } from '../../drivers/entities/driver.entity';
import { CancellationPenaltyService } from './cancellation-penalty.service';
import { DriverBehaviorService } from '../../drivers/services/driver-behavior.service';
import { DriverMovementService } from '../../drivers/services/driver-movement.service';
import { DriverStatusService } from '../../drivers/services/driver-status.service';
import { WebSocketIntegrationService } from '../../websockets/services/websocket-integration.service';
import {
  CancellationResponseDto,
  CancellationDetailsDto,
} from '../dto/cancel-ride.dto';
import { DriverStatusResponseDto } from '../../drivers/dto/driver-location.dto';

@Injectable()
export class RideCancellationService {
  private readonly logger = new Logger(RideCancellationService.name);

  constructor(
    @InjectRepository(Ride)
    private rideRepository: Repository<Ride>,
    @InjectRepository(RideCancellation)
    private cancellationRepository: Repository<RideCancellation>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Driver)
    private driverRepository: Repository<Driver>,
    private cancellationPenaltyService: CancellationPenaltyService,
    private driverBehaviorService: DriverBehaviorService,
    private driverMovementService: DriverMovementService,
    private driverStatusService: DriverStatusService,
    private webSocketIntegrationService: WebSocketIntegrationService,
  ) {}

  async cancelRide(
    rideId: number,
    userId: number,
    reason: string,
  ): Promise<CancellationResponseDto> {
    // Get ride with related data
    const ride = await this.rideRepository.findOne({
      where: { id: rideId },
      relations: ['passenger', 'driver'],
    });

    if (!ride) {
      throw new NotFoundException('Ride not found');
    }

    // Validate user can cancel this ride
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (ride.passengerId !== userId && ride.driverId !== userId) {
      throw new BadRequestException('You can only cancel your own rides');
    }

    // Check if ride can be cancelled
    if (
      [
        RideStatus.COMPLETED,
        RideStatus.CANCELLED,
        RideStatus.CANCELLED_BY_DRIVER,
        RideStatus.CANCELLED_BY_PASSENGER,
      ].includes(ride.status)
    ) {
      throw new BadRequestException('This ride cannot be cancelled');
    }

    // Determine cancellation type
    const cancellationType =
      user.role === UserRole.DRIVER
        ? CancellationType.DRIVER
        : CancellationType.PASSENGER;

    // Calculate penalty (only for passenger cancellations)
    const penaltyResult =
      await this.cancellationPenaltyService.calculatePenalty(ride, user);

    // Create cancellation record
    const cancellation = this.cancellationRepository.create({
      rideId,
      cancelledBy: userId,
      cancellationType,
      reason,
      penaltyApplied: penaltyResult.shouldApply,
      penaltyAmount: penaltyResult.penaltyAmount,
      driverDistanceFromPickup: penaltyResult.distanceFromPickup,
      driverDistanceFromInitial: penaltyResult.distanceFromInitial,
      movementDetected: penaltyResult.movementDetected,
    });

    await this.cancellationRepository.save(cancellation);

    // Update ride status
    const newStatus =
      cancellationType === CancellationType.DRIVER
        ? RideStatus.CANCELLED_BY_DRIVER
        : RideStatus.CANCELLED_BY_PASSENGER;

    await this.rideRepository.update(rideId, {
      status: newStatus,
      cancelledBy: userId,
      cancelledAt: new Date(),
      cancellationReason: reason,
      penaltyAmount: penaltyResult.penaltyAmount,
    });

    // Track driver behavior if driver cancelled
    let driverBehaviorImpact;
    if (cancellationType === CancellationType.DRIVER) {
      await this.driverBehaviorService.trackDriverCancellation(
        ride.driverId,
        rideId,
        reason,
      );
      const behaviorScore =
        await this.driverBehaviorService.calculateBehaviorScore(ride.driverId);
      const isEligible = await this.driverBehaviorService.isDriverEligible(
        ride.driverId,
      );

      driverBehaviorImpact = {
        flagged: !isEligible,
        suspended: !isEligible,
        behaviorScore: behaviorScore.score,
      };

      // Update driver status to available when driver cancels (unless suspended)
      if (!driverBehaviorImpact.suspended) {
        try {
          await this.driverStatusService.updateDriverStatus(
            ride.driverId,
            { status: DriverStatus.AVAILABLE },
            'ride_cancelled_by_driver',
          );
          this.logger.log(`Updated driver ${ride.driverId} status to AVAILABLE after driver cancellation`);
        } catch (error) {
          this.logger.error(`Failed to update driver ${ride.driverId} status after driver cancellation:`, error.message);
        }
      } else {
        this.logger.log(`Driver ${ride.driverId} remains suspended after cancellation`);
      }
    }

    // Send WebSocket notification to driver when passenger cancels
    if (cancellationType === CancellationType.PASSENGER && ride.driverId) {
      try {
        await this.webSocketIntegrationService.broadcastRideCancelled(
          rideId,
          'passenger',
          reason,
          {
            penaltyApplied: penaltyResult.shouldApply,
            penaltyAmount: penaltyResult.penaltyAmount,
          },
        );
        this.logger.log(`Notified driver ${ride.driverId} about ride ${rideId} cancellation by passenger`);
      } catch (error) {
        this.logger.error(`Failed to notify driver ${ride.driverId} about ride ${rideId} cancellation:`, error.message);
      }

      // Update driver status to available since ride is cancelled
      try {
        await this.driverStatusService.updateDriverStatus(
          ride.driverId,
          { status: DriverStatus.AVAILABLE },
          'ride_cancelled_by_passenger',
        );
        this.logger.log(`Updated driver ${ride.driverId} status to AVAILABLE after passenger cancellation`);
      } catch (error) {
        this.logger.error(`Failed to update driver ${ride.driverId} status after cancellation:`, error.message);
      }
    }

    return {
      success: true,
      message: penaltyResult.shouldApply
        ? 'Ride cancelled with penalty applied'
        : 'Ride cancelled successfully',
      penaltyApplied: penaltyResult.shouldApply,
      penaltyAmount: penaltyResult.penaltyAmount,
      cancellationId: cancellation.id,
      driverBehaviorImpact,
    };
  }

  async getCancellationDetails(
    rideId: number,
  ): Promise<CancellationDetailsDto> {
    const cancellation = await this.cancellationRepository.findOne({
      where: { rideId },
      relations: ['cancelledByUser'],
    });

    if (!cancellation) {
      throw new NotFoundException('Cancellation not found');
    }

    return {
      cancellationId: cancellation.id,
      cancelledBy: {
        id: cancellation.cancelledBy,
        name: `${cancellation.cancelledByUser.firstName} ${cancellation.cancelledByUser.lastName}`,
        type: cancellation.cancellationType,
      },
      reason: cancellation.reason,
      penaltyAmount: cancellation.penaltyAmount,
      cancellationTime: cancellation.createdAt,
      movementDetected: cancellation.movementDetected,
      driverDistanceFromPickup: cancellation.driverDistanceFromPickup,
      driverDistanceFromInitial: cancellation.driverDistanceFromInitial,
      canAppeal: false, // Future feature
    };
  }

  async getDriverStatus(rideId: number): Promise<DriverStatusResponseDto> {
    const ride = await this.rideRepository.findOne({
      where: { id: rideId },
      relations: ['driver'],
    });

    if (!ride) {
      throw new NotFoundException('Ride not found');
    }

    const driver = ride.driver;
    const movementValidation =
      await this.driverMovementService.validateMovementFromInitial(rideId);
    const behaviorScore =
      await this.driverBehaviorService.calculateBehaviorScore(driver.id);

    // Get recent movement history (last 10 points)
    const movementHistory = await this.driverMovementService[
      'getMovementHistory'
    ](driver.id, rideId);
    const recentHistory = movementHistory.slice(-10).map((point) => ({
      latitude: point.latitude,
      longitude: point.longitude,
      timestamp: point.timestamp,
      distanceFromPickup: 0, // Would be calculated
    }));

    return {
      driverLocation: {
        latitude: driver.currentLatitude,
        longitude: driver.currentLongitude,
        lastUpdate: driver.lastLocationUpdate,
      },
      hasStartedMoving: movementValidation.hasMovedFromInitial,
      distanceFromPickup: movementValidation.distanceFromPickup,
      estimatedArrival: this.calculateEstimatedArrival(
        movementValidation.distanceFromPickup,
      ),
      movementHistory: recentHistory,
      driverReliability: {
        cancellationRate: driver.cancellationRate,
        totalRides: driver.totalRides,
        isFlagged: driver.isFlagged,
        behaviorScore: behaviorScore.score,
      },
    };
  }

  private calculateEstimatedArrival(distanceKm: number): string {
    const averageSpeedKmh = 30;
    const timeHours = distanceKm / averageSpeedKmh;
    const timeMinutes = Math.ceil(timeHours * 60);

    if (timeMinutes <= 1) {
      return 'Arriving now';
    } else if (timeMinutes <= 60) {
      return `${timeMinutes} minutes`;
    } else {
      const hours = Math.floor(timeMinutes / 60);
      const remainingMinutes = timeMinutes % 60;
      return `${hours}h ${remainingMinutes}m`;
    }
  }
}

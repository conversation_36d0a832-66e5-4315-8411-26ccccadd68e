import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface VerificationRequirements {
  emailVerificationRequired: boolean;
  phoneVerificationRequired: boolean;
  documentVerificationRequired: boolean;
  autoApprove: boolean;
}

export interface VerificationConfig {
  passenger: VerificationRequirements;
  driver: VerificationRequirements;
}

@Injectable()
export class VerificationConfigService {
  private readonly config: VerificationConfig;

  constructor(private configService: ConfigService) {
    this.config = this.loadConfiguration();
  }

  private loadConfiguration(): VerificationConfig {
    return {
      passenger: {
        emailVerificationRequired: this.configService.get<boolean>('PASSENGER_EMAIL_VERIFICATION_REQUIRED', false),
        phoneVerificationRequired: this.configService.get<boolean>('PASSENGER_PHONE_VERIFICATION_REQUIRED', false),
        documentVerificationRequired: this.configService.get<boolean>('PASSENGER_DOCUMENT_VERIFICATION_REQUIRED', false),
        autoApprove: this.configService.get<boolean>('PASSENGER_AUTO_APPROVE', true),
      },
      driver: {
        emailVerificationRequired: this.configService.get<boolean>('DRIVER_EMAIL_VERIFICATION_REQUIRED', false),
        phoneVerificationRequired: this.configService.get<boolean>('DRIVER_PHONE_VERIFICATION_REQUIRED', false),
        documentVerificationRequired: this.configService.get<boolean>('DRIVER_DOCUMENT_VERIFICATION_REQUIRED', true),
        autoApprove: this.configService.get<boolean>('DRIVER_AUTO_APPROVE', false),
      },
    };
  }

  /**
   * Get verification requirements for a specific user role
   */
  getVerificationRequirements(role: 'passenger' | 'driver'): VerificationRequirements {
    return this.config[role];
  }

  /**
   * Check if email verification is required for a user role
   */
  isEmailVerificationRequired(role: 'passenger' | 'driver'): boolean {
    return this.config[role].emailVerificationRequired;
  }

  /**
   * Check if phone verification is required for a user role
   */
  isPhoneVerificationRequired(role: 'passenger' | 'driver'): boolean {
    return this.config[role].phoneVerificationRequired;
  }

  /**
   * Check if document verification is required for a user role
   */
  isDocumentVerificationRequired(role: 'passenger' | 'driver'): boolean {
    return this.config[role].documentVerificationRequired;
  }

  /**
   * Check if auto-approval is enabled for a user role
   */
  isAutoApprovalEnabled(role: 'passenger' | 'driver'): boolean {
    return this.config[role].autoApprove;
  }

  /**
   * Check if a user can activate their account based on current verification status
   */
  canUserActivate(
    role: 'passenger' | 'driver',
    emailVerified: boolean,
    phoneVerified: boolean,
    documentsVerified: boolean,
  ): boolean {
    const requirements = this.getVerificationRequirements(role);

    // If auto-approval is enabled, user can always activate
    if (requirements.autoApprove) {
      return true;
    }

    // Check all required verifications
    const emailOk = !requirements.emailVerificationRequired || emailVerified;
    const phoneOk = !requirements.phoneVerificationRequired || phoneVerified;
    const documentsOk = !requirements.documentVerificationRequired || documentsVerified;

    return emailOk && phoneOk && documentsOk;
  }

  /**
   * Get the user status that should be set during registration
   */
  getRegistrationUserStatus(role: 'passenger' | 'driver'): 'active' | 'pending_verification' {
    return this.isAutoApprovalEnabled(role) ? 'active' : 'pending_verification';
  }

  /**
   * Get the registration status that should be set during registration
   */
  getRegistrationStatus(role: 'passenger' | 'driver'): 'completed' | 'pending_verification' {
    return this.isAutoApprovalEnabled(role) ? 'completed' : 'pending_verification';
  }

  /**
   * Get next steps for a user based on their verification status and requirements
   */
  getNextSteps(
    role: 'passenger' | 'driver',
    emailVerified: boolean,
    phoneVerified: boolean,
    documentsVerified: boolean,
  ): string[] {
    const requirements = this.getVerificationRequirements(role);
    const steps: string[] = [];

    // If auto-approval is enabled, provide optional steps
    if (requirements.autoApprove) {
      steps.push('You can start using the app immediately');
      if (!emailVerified) {
        steps.push('Optionally verify your email for account security');
      }
      if (!phoneVerified) {
        steps.push('Optionally verify your phone number for notifications');
      }
      if (!documentsVerified && role === 'driver') {
        steps.push('Optionally upload documents for full driver verification');
      }
      return steps;
    }

    // Required steps based on configuration
    if (requirements.emailVerificationRequired && !emailVerified) {
      steps.push('Verify your email address');
    }
    if (requirements.phoneVerificationRequired && !phoneVerified) {
      steps.push('Verify your phone number');
    }
    if (requirements.documentVerificationRequired && !documentsVerified) {
      if (role === 'driver') {
        steps.push('Upload and verify your driver documents');
      } else {
        steps.push('Upload and verify your documents');
      }
    }

    if (steps.length === 0) {
      steps.push('Your account is ready to use');
    }

    return steps;
  }

  /**
   * Get registration success message based on configuration
   */
  getRegistrationMessage(role: 'passenger' | 'driver'): string {
    if (this.isAutoApprovalEnabled(role)) {
      return `Registration successful! You can now start using the app. ${
        role === 'driver' ? 'Document verification is optional but recommended.' : 'Email and phone verification are optional but recommended.'
      }`;
    }

    const requirements = this.getVerificationRequirements(role);
    const requiredSteps: string[] = [];

    if (requirements.emailVerificationRequired) requiredSteps.push('email');
    if (requirements.phoneVerificationRequired) requiredSteps.push('phone number');
    if (requirements.documentVerificationRequired) requiredSteps.push('documents');

    if (requiredSteps.length === 0) {
      return 'Registration successful! Your account is ready to use.';
    }

    return `Registration successful. Please verify your ${requiredSteps.join(' and ')}.`;
  }

  /**
   * Get current configuration for debugging/admin purposes
   */
  getCurrentConfig(): VerificationConfig {
    return { ...this.config };
  }
}

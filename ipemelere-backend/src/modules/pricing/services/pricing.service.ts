import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PricingConfiguration, PricingModel } from '../entities/pricing-configuration.entity';
import { VehicleType } from '../../vehicles/entities/vehicle.entity';
import { 
  PriceCalculationRequest, 
  PriceCalculationResponse, 
  RideOptionPricing,
  PriceBreakdown,
  PRICING_CONSTANTS 
} from '../interfaces/pricing.interface';
import { SearchRideDto, RideOptionDto } from '../../rides/dto/search-ride.dto';

@Injectable()
export class PricingService {
  private readonly logger = new Logger(PricingService.name);
  private cachedConfiguration: PricingConfiguration | null = null;
  private cacheTimestamp: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor(
    @InjectRepository(PricingConfiguration)
    private pricingConfigRepository: Repository<PricingConfiguration>,
  ) {}

  /**
   * Calculate ride price based on current pricing configuration
   */
  async calculateRidePrice(request: PriceCalculationRequest): Promise<PriceCalculationResponse> {
    try {
      const config = await this.getActivePricingConfiguration();

      const distance = request.distance || this.calculateDistance(
        request.pickupLatitude,
        request.pickupLongitude,
        request.dropoffLatitude,
        request.dropoffLongitude
      );

      let basePrice = 0;
      let distancePrice = 0;
      let breakdown: PriceBreakdown;
      let pricingModel = config.model;

      if (config.model === PricingModel.VEHICLE_TYPE_BASED && request.vehicleType && config.vehicleTypePricing) {
        const vehiclePrice = config.vehicleTypePricing[request.vehicleType];
        if (vehiclePrice) {
          basePrice = Number(vehiclePrice.basePrice) || 0;
          distancePrice = distance * (Number(vehiclePrice.pricePerKm) || 0);

          breakdown = {
            basePrice: Number(vehiclePrice.basePrice) || 0,
            distanceCharge: distancePrice,
            minimumFare: Number(vehiclePrice.minimumFare) || 0,
            surgeMultiplier: Number(vehiclePrice.surgeMultiplier) || 1,
          };
        } else {
          // Fallback to distance-only if vehicle type not configured
          basePrice = Number(config.basePrice) || 0;
          distancePrice = distance * (Number(config.pricePerKm) || 0);
          pricingModel = PricingModel.DISTANCE_ONLY;

          breakdown = {
            basePrice: Number(config.basePrice) || 0,
            distanceCharge: distancePrice,
          };
        }
      } else {
        // Distance-only pricing
        // Ensure values are numbers (database might return strings)
        basePrice = Number(config.basePrice) || 0;
        distancePrice = distance * (Number(config.pricePerKm) || 0);

        breakdown = {
          basePrice: Number(config.basePrice) || 0,
          distanceCharge: distancePrice,
        };
      }

      let totalPrice = basePrice + distancePrice;

      // Apply surge multiplier if present
      if (breakdown.surgeMultiplier && breakdown.surgeMultiplier > 1) {
        totalPrice *= breakdown.surgeMultiplier;
        breakdown.surgeMultiplier = breakdown.surgeMultiplier;
      }

      // Apply minimum fare if configured
      if (breakdown.minimumFare && totalPrice < breakdown.minimumFare) {
        totalPrice = breakdown.minimumFare;
      }

      // Apply time-based multipliers if configured
      if (config.additionalFactors?.timeOfDayMultipliers && request.requestTime) {
        const timeMultiplier = this.getTimeBasedMultiplier(config.additionalFactors.timeOfDayMultipliers, request.requestTime);
        if (timeMultiplier > 1) {
          totalPrice *= timeMultiplier;
          breakdown.timeMultiplier = timeMultiplier;
        }
      }

      const finalPrice = Math.round(totalPrice * 100) / 100;

      // Validate the final price
      if (isNaN(finalPrice) || finalPrice === null || finalPrice === undefined || finalPrice <= 0) {
        this.logger.error('Invalid final price calculated', {
          finalPrice,
          configValues: { basePrice: config.basePrice, pricePerKm: config.pricePerKm },
          calculationValues: { basePrice, distancePrice, distance },
          request
        });
        throw new Error('Invalid price calculation result');
      }

      return {
        basePrice,
        distancePrice,
        totalPrice: finalPrice,
        distance: Math.round(distance * 100) / 100,
        vehicleType: request.vehicleType,
        pricingModel,
        breakdown,
        currency: PRICING_CONSTANTS.CURRENCY,
      };
    } catch (error) {
      this.logger.error('Error calculating ride price', error);
      return this.getFallbackPriceCalculation(request);
    }
  }

  /**
   * Get available ride options with pricing for search
   */
  async getRideOptions(searchRequest: SearchRideDto): Promise<RideOptionDto[]> {
    try {
      const config = await this.getActivePricingConfiguration();
      const distance = this.calculateDistance(
        searchRequest.pickupLatitude,
        searchRequest.pickupLongitude,
        searchRequest.dropoffLatitude,
        searchRequest.dropoffLongitude,
      );

      const options: RideOptionDto[] = [];

      if (config.model === PricingModel.VEHICLE_TYPE_BASED && config.vehicleTypePricing) {
        // Create options for each configured vehicle type
        for (const [vehicleType, pricing] of Object.entries(config.vehicleTypePricing)) {
          if (pricing) {
            const priceCalc = await this.calculateRidePrice({
              pickupLatitude: searchRequest.pickupLatitude,
              pickupLongitude: searchRequest.pickupLongitude,
              dropoffLatitude: searchRequest.dropoffLatitude,
              dropoffLongitude: searchRequest.dropoffLongitude,
              vehicleType: vehicleType as VehicleType,
              distance,
            });

            options.push({
              id: `${vehicleType}_${Date.now()}`,
              type: vehicleType as any,
              price: priceCalc.totalPrice,
              eta: this.calculateETA(vehicleType as VehicleType),
              distance: priceCalc.distance,
              duration: Math.round(distance * 2), // Simplified duration calculation
            });
          }
        }
      } else {
        // Distance-only pricing - create a single option
        const priceCalc = await this.calculateRidePrice({
          pickupLatitude: searchRequest.pickupLatitude,
          pickupLongitude: searchRequest.pickupLongitude,
          dropoffLatitude: searchRequest.dropoffLatitude,
          dropoffLongitude: searchRequest.dropoffLongitude,
          distance,
        });

        options.push({
          id: `standard_${Date.now()}`,
          type: VehicleType.SEDAN as any, // Default type for display
          price: priceCalc.totalPrice,
          eta: this.calculateETA(VehicleType.SEDAN),
          distance: priceCalc.distance,
          duration: Math.round(distance * 2),
        });
      }

      return options;
    } catch (error) {
      this.logger.error('Error getting ride options', error);
      return this.getFallbackRideOptions(searchRequest);
    }
  }

  /**
   * Get the currently active pricing configuration
   */
  async getActivePricingConfiguration(): Promise<PricingConfiguration> {
    // Check cache first
    if (this.cachedConfiguration && (Date.now() - this.cacheTimestamp) < this.CACHE_TTL) {
      return this.cachedConfiguration;
    }

    try {
      const config = await this.pricingConfigRepository.findOne({
        where: { 
          isActive: true,
          recordStatus: 'Active' as any
        },
        order: { priority: 'DESC', createdAt: 'DESC' }
      });

      if (config) {
        // Update cache
        this.cachedConfiguration = config;
        this.cacheTimestamp = Date.now();
        return config;
      }

      this.logger.warn('No active pricing configuration found, using fallback');
      return this.getFallbackConfiguration();
    } catch (error) {
      this.logger.error('Error fetching pricing configuration', error);
      return this.getFallbackConfiguration();
    }
  }

  /**
   * Update pricing configuration and invalidate cache
   */
  async updatePricingConfiguration(configData: Partial<PricingConfiguration>): Promise<PricingConfiguration> {
    try {
      // If setting a new active configuration, deactivate others
      if (configData.isActive) {
        await this.pricingConfigRepository.update(
          { isActive: true },
          { isActive: false }
        );
      }

      let config: PricingConfiguration;
      
      if (configData.id) {
        // Update existing configuration
        await this.pricingConfigRepository.update(configData.id, configData);
        config = await this.pricingConfigRepository.findOne({ where: { id: configData.id } });
        if (!config) {
          throw new NotFoundException('Pricing configuration not found');
        }
      } else {
        // Create new configuration
        config = this.pricingConfigRepository.create(configData);
        config = await this.pricingConfigRepository.save(config);
      }

      // Invalidate cache
      this.invalidateCache();

      this.logger.log(`Pricing configuration updated: ${config.name}`);
      return config;
    } catch (error) {
      this.logger.error('Error updating pricing configuration', error);
      throw error;
    }
  }

  /**
   * Invalidate the pricing configuration cache
   */
  invalidateCache(): void {
    this.cachedConfiguration = null;
    this.cacheTimestamp = 0;
  }

  /**
   * Calculate distance between two points using Haversine formula
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    // Validate input coordinates
    if (isNaN(lat1) || isNaN(lon1) || isNaN(lat2) || isNaN(lon2)) {
      this.logger.error('Invalid coordinates for distance calculation', { lat1, lon1, lat2, lon2 });
      return 1; // Return 1km as fallback
    }

    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    // Validate result
    if (isNaN(distance) || distance < 0) {
      this.logger.error('Invalid distance calculated', { distance, coordinates: { lat1, lon1, lat2, lon2 } });
      return 1; // Return 1km as fallback
    }

    return distance;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Calculate ETA based on vehicle type (simplified)
   */
  private calculateETA(vehicleType: VehicleType): string {
    const baseETA = Math.floor(Math.random() * 10) + 5; // 5-15 minutes
    return `${baseETA} mins`;
  }

  /**
   * Get time-based multiplier for pricing
   */
  private getTimeBasedMultiplier(multipliers: any[], requestTime: Date): number {
    const hour = requestTime.getHours();
    const dayOfWeek = requestTime.getDay();

    for (const multiplier of multipliers) {
      if (hour >= multiplier.startHour && hour <= multiplier.endHour) {
        if (!multiplier.dayOfWeek || multiplier.dayOfWeek.includes(dayOfWeek)) {
          return multiplier.multiplier;
        }
      }
    }

    return 1; // No multiplier
  }

  /**
   * Get fallback pricing configuration when database is unavailable
   */
  private getFallbackConfiguration(): PricingConfiguration {
    const fallback = new PricingConfiguration();
    fallback.name = PRICING_CONSTANTS.FALLBACK_CONFIGURATION_NAME;
    fallback.isActive = true;
    fallback.model = PricingModel.DISTANCE_ONLY;
    fallback.basePrice = PRICING_CONSTANTS.DEFAULT_BASE_PRICE;
    fallback.pricePerKm = PRICING_CONSTANTS.DEFAULT_PRICE_PER_KM;
    fallback.description = 'Emergency fallback configuration';
    
    return fallback;
  }

  /**
   * Get fallback price calculation when service fails
   */
  private getFallbackPriceCalculation(request: PriceCalculationRequest): PriceCalculationResponse {
    const distance = request.distance || this.calculateDistance(
      request.pickupLatitude,
      request.pickupLongitude,
      request.dropoffLatitude,
      request.dropoffLongitude
    );

    const basePrice = PRICING_CONSTANTS.DEFAULT_BASE_PRICE;
    const distancePrice = distance * PRICING_CONSTANTS.DEFAULT_PRICE_PER_KM;
    const totalPrice = Math.max(basePrice + distancePrice, PRICING_CONSTANTS.DEFAULT_MINIMUM_FARE);

    return {
      basePrice,
      distancePrice,
      totalPrice: Math.round(totalPrice * 100) / 100,
      distance: Math.round(distance * 100) / 100,
      vehicleType: request.vehicleType,
      pricingModel: PricingModel.DISTANCE_ONLY,
      breakdown: {
        basePrice,
        distanceCharge: distancePrice,
        minimumFare: PRICING_CONSTANTS.DEFAULT_MINIMUM_FARE,
      },
      currency: PRICING_CONSTANTS.CURRENCY,
    };
  }

  /**
   * Get fallback ride options when service fails
   */
  private getFallbackRideOptions(searchRequest: SearchRideDto): RideOptionDto[] {
    const distance = this.calculateDistance(
      searchRequest.pickupLatitude,
      searchRequest.pickupLongitude,
      searchRequest.dropoffLatitude,
      searchRequest.dropoffLongitude,
    );

    const basePrice = PRICING_CONSTANTS.DEFAULT_BASE_PRICE;
    const totalPrice = Math.max(
      basePrice + (distance * PRICING_CONSTANTS.DEFAULT_PRICE_PER_KM),
      PRICING_CONSTANTS.DEFAULT_MINIMUM_FARE
    );

    return [{
      id: `fallback_${Date.now()}`,
      type: VehicleType.SEDAN as any,
      price: Math.round(totalPrice * 100) / 100,
      eta: '10 mins',
      distance: Math.round(distance * 100) / 100,
      duration: Math.round(distance * 2),
    }];
  }
}
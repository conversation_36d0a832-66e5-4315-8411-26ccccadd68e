import { VehicleType } from '../../vehicles/entities/vehicle.entity';
import { PricingModel, VehicleTypePricing } from '../entities/pricing-configuration.entity';

export interface PriceCalculationRequest {
  pickupLatitude: number;
  pickupLongitude: number;
  dropoffLatitude: number;
  dropoffLongitude: number;
  vehicleType?: VehicleType;
  distance?: number; // Pre-calculated distance in km
  requestTime?: Date;
  passengerCount?: number;
}

export interface PriceCalculationResponse {
  basePrice: number;
  distancePrice: number;
  totalPrice: number;
  distance: number;
  vehicleType?: VehicleType;
  pricingModel: PricingModel;
  breakdown: PriceBreakdown;
  currency: string;
}

export interface PriceBreakdown {
  basePrice: number;
  distanceCharge: number;
  timeMultiplier?: number;
  surgeMultiplier?: number;
  minimumFare?: number;
  additionalCharges?: AdditionalCharge[];
}

export interface AdditionalCharge {
  name: string;
  amount: number;
  description?: string;
}

export interface RideOptionPricing {
  vehicleType: VehicleType;
  price: number;
  eta: string;
  distance: number;
  duration: number;
  pricingModel: PricingModel;
  breakdown: PriceBreakdown;
}

export interface PricingValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface PricingConfigurationSummary {
  id: number;
  name: string;
  isActive: boolean;
  model: PricingModel;
  basePrice: number;
  pricePerKm: number;
  vehicleTypesConfigured: VehicleType[];
  validFrom?: Date;
  validTo?: Date;
  description?: string;
}

export interface PricingDefaults {
  basePrice: number;
  pricePerKm: number;
  minimumFare: number;
  currency: string;
  vehicleTypePricing: VehicleTypePricing;
}

// Constants for Malawian Kwacha pricing
export const PRICING_CONSTANTS = {
  CURRENCY: 'MWK',
  DEFAULT_BASE_PRICE: 3000,
  DEFAULT_PRICE_PER_KM: 3000,
  DEFAULT_MINIMUM_FARE: 3000,
  FALLBACK_CONFIGURATION_NAME: 'emergency_fallback',
} as const;
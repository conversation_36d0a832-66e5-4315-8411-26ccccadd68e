import { Entity, Column } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '../../../common/entities/base.entity';
import { VehicleType } from '../../vehicles/entities/vehicle.entity';

export enum PricingModel {
  VEHICLE_TYPE_BASED = 'vehicle_type_based',
  DISTANCE_ONLY = 'distance_only',
  HYBRID = 'hybrid',
}

export interface VehicleTypePrice {
  basePrice: number;
  pricePerKm: number;
  minimumFare: number;
  surgeMultiplier?: number;
}

export interface VehicleTypePricing {
  [VehicleType.SEDAN]?: VehicleTypePrice;
  [VehicleType.SUV]?: VehicleTypePrice;
  [VehicleType.HATCHBACK]?: VehicleTypePrice;
  [VehicleType.LUXURY]?: VehicleTypePrice;
  [VehicleType.MOTORCYCLE]?: VehicleTypePrice;
  [VehicleType.PICKUP]?: VehicleTypePrice;
  [VehicleType.MINIBUS]?: VehicleTypePrice;
}

export interface TimeBasedMultiplier {
  startHour: number;
  endHour: number;
  multiplier: number;
  dayOfWeek?: number[]; // 0-6, Sunday to Saturday
}

export interface DistanceThreshold {
  minDistance: number;
  maxDistance: number;
  pricePerKm: number;
}

export interface AdditionalPricingFactors {
  timeOfDayMultipliers?: TimeBasedMultiplier[];
  distanceThresholds?: DistanceThreshold[];
  demandMultiplier?: number;
}

@Entity('pricing_configurations')
export class PricingConfiguration extends BaseEntity {
  @ApiProperty({ description: 'Unique name for the pricing configuration' })
  @Column({ unique: true })
  name: string;

  @ApiProperty({ description: 'Whether this configuration is currently active' })
  @Column({ default: true })
  isActive: boolean;

  @ApiProperty({
    enum: PricingModel,
    description: 'The pricing model to use for calculations',
  })
  @Column({
    type: 'enum',
    enum: PricingModel,
    default: PricingModel.DISTANCE_ONLY,
  })
  model: PricingModel;

  @ApiProperty({
    description: 'Base price in Malawian Kwacha for distance-only model',
    example: 3000,
  })
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 3000.0,
  })
  basePrice: number;

  @ApiProperty({
    description: 'Price per kilometer in Malawian Kwacha',
    example: 3000,
  })
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 3000.0,
  })
  pricePerKm: number;

  @ApiProperty({
    description: 'Vehicle-specific pricing configuration',
    type: 'object',
    additionalProperties: true,
    nullable: true,
  })
  @Column({
    type: 'json',
    nullable: true,
  })
  vehicleTypePricing: VehicleTypePricing;

  @ApiProperty({
    description: 'Additional pricing factors for future extensibility',
    type: 'object',
    additionalProperties: true,
    nullable: true,
  })
  @Column({
    type: 'json',
    nullable: true,
  })
  additionalFactors: AdditionalPricingFactors;

  @ApiProperty({
    description: 'When this configuration becomes valid',
    nullable: true,
  })
  @Column({
    type: 'timestamp',
    nullable: true,
  })
  validFrom: Date;

  @ApiProperty({
    description: 'When this configuration expires',
    nullable: true,
  })
  @Column({
    type: 'timestamp',
    nullable: true,
  })
  validTo: Date;

  @ApiProperty({
    description: 'Description of this pricing configuration',
    nullable: true,
  })
  @Column({
    type: 'text',
    nullable: true,
  })
  description: string;

  @ApiProperty({
    description: 'Priority order when multiple configurations are active',
    default: 0,
  })
  @Column({
    type: 'int',
    default: 0,
  })
  priority: number;
}
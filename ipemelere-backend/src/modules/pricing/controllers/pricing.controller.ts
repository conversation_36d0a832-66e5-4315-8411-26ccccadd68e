import {
  Controller,
  Get,
  Put,
  Post,
  Body,
  Param,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { PricingService } from '../services/pricing.service';
import { PricingConfiguration, VehicleTypePrice } from '../entities/pricing-configuration.entity';
import { VehicleType } from '../../vehicles/entities/vehicle.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/entities/user.entity';

import { 
  UpdatePricingConfigDto,
  ValidationResult,
  PricingHealthStatusDto,
  VehicleTypePricingUpdateResponseDto,
  ConfigurationActivationResponseDto
} from '../dto';

@ApiTags('Admin - Pricing Management')
@Controller('admin/pricing')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
@ApiBearerAuth()
export class PricingController {
  constructor(private readonly pricingService: PricingService) {}

  @Get('configuration')
  @ApiOperation({ summary: 'Get current active pricing configuration' })
  @ApiResponse({
    status: 200,
    description: 'Current pricing configuration retrieved successfully',
    type: PricingConfiguration,
  })
  @ApiResponse({
    status: 404,
    description: 'No active pricing configuration found',
  })
  async getCurrentConfiguration(): Promise<PricingConfiguration> {
    try {
      return await this.pricingService.getActivePricingConfiguration();
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve pricing configuration',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('configuration')
  @ApiOperation({ summary: 'Update pricing configuration' })
  @ApiResponse({
    status: 200,
    description: 'Pricing configuration updated successfully',
    type: PricingConfiguration,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid pricing configuration data',
  })
  async updateConfiguration(
    @Body() config: UpdatePricingConfigDto,
  ): Promise<PricingConfiguration> {
    try {
      // Validate the configuration before updating
      const validation = await this.validateConfiguration(config);
      if (!validation.isValid) {
        throw new HttpException(
          {
            message: 'Invalid pricing configuration',
            errors: validation.errors,
            warnings: validation.warnings,
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      return await this.pricingService.updatePricingConfiguration(config);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to update pricing configuration',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('configuration/validate')
  @ApiOperation({ summary: 'Validate pricing configuration without saving' })
  @ApiResponse({
    status: 200,
    description: 'Configuration validation result',
  })
  validateConfiguration(
    @Body() config: UpdatePricingConfigDto,
  ): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate base price
    if (config.basePrice !== undefined) {
      if (config.basePrice < 0) {
        errors.push('Base price cannot be negative');
      }
      if (config.basePrice < 2000) {
        warnings.push('Base price is quite low for Malawian Kwacha (< 2000 MWK)');
      }
    }

    // Validate price per km
    if (config.pricePerKm !== undefined) {
      if (config.pricePerKm < 0) {
        errors.push('Price per km cannot be negative');
      }
      if (config.pricePerKm < 1000) {
        warnings.push('Price per km is quite low for Malawian Kwacha (< 1000 MWK)');
      }
    }

    // Validate vehicle type pricing
    if (config.vehicleTypePricing) {
      for (const [vehicleType, pricing] of Object.entries(config.vehicleTypePricing)) {
        if (!Object.values(VehicleType).includes(vehicleType as VehicleType)) {
          errors.push(`Invalid vehicle type: ${vehicleType}`);
          continue;
        }

        const typedPricing = pricing as VehicleTypePrice;
        if (typedPricing.basePrice < 0) {
          errors.push(`Base price for ${vehicleType} cannot be negative`);
        }
        if (typedPricing.pricePerKm < 0) {
          errors.push(`Price per km for ${vehicleType} cannot be negative`);
        }
        if (typedPricing.minimumFare < 0) {
          errors.push(`Minimum fare for ${vehicleType} cannot be negative`);
        }
        if (typedPricing.minimumFare > typedPricing.basePrice) {
          warnings.push(`Minimum fare for ${vehicleType} is higher than base price`);
        }
      }
    }

    // Validate date ranges
    if (config.validFrom && config.validTo) {
      if (config.validFrom >= config.validTo) {
        errors.push('Valid from date must be before valid to date');
      }
    }

    // Validate name
    if (config.name !== undefined) {
      if (!config.name || config.name.trim().length === 0) {
        errors.push('Configuration name cannot be empty');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  @Get('vehicle-types')
  @ApiOperation({ summary: 'Get vehicle type pricing configuration' })
  @ApiResponse({
    status: 200,
    description: 'Vehicle type pricing retrieved successfully',
  })
  async getVehicleTypePricing(): Promise<any> {
    try {
      const config = await this.pricingService.getActivePricingConfiguration();
      return config.vehicleTypePricing || {};
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve vehicle type pricing',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('vehicle-types/:type')
  @ApiOperation({ summary: 'Update pricing for a specific vehicle type' })
  @ApiResponse({
    status: 200,
    description: 'Vehicle type pricing updated successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid vehicle type or pricing data',
  })
  async updateVehicleTypePrice(
    @Param('type') type: string,
    @Body() price: VehicleTypePrice,
  ): Promise<VehicleTypePricingUpdateResponseDto> {
    try {
      // Validate vehicle type
      if (!Object.values(VehicleType).includes(type as VehicleType)) {
        throw new HttpException(
          `Invalid vehicle type: ${type}`,
          HttpStatus.BAD_REQUEST,
        );
      }

      // Validate pricing data
      if (price.basePrice < 0 || price.pricePerKm < 0 || price.minimumFare < 0) {
        throw new HttpException(
          'Pricing values cannot be negative',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Get current configuration
      const config = await this.pricingService.getActivePricingConfiguration();
      
      // Update vehicle type pricing
      const updatedVehicleTypePricing = {
        ...config.vehicleTypePricing,
        [type]: price,
      };

      await this.pricingService.updatePricingConfiguration({
        id: config.id,
        vehicleTypePricing: updatedVehicleTypePricing,
      });

      return {
        message: `Pricing for ${type} updated successfully`,
        vehicleType: type,
        pricing: price,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to update vehicle type pricing',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('configurations')
  @ApiOperation({ summary: 'Get all pricing configurations' })
  @ApiResponse({
    status: 200,
    description: 'All pricing configurations retrieved successfully',
  })
  async getAllConfigurations(): Promise<PricingConfiguration[]> {
    try {
      // This would require a new method in PricingService
      // For now, return the active configuration
      const activeConfig = await this.pricingService.getActivePricingConfiguration();
      return [activeConfig];
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve pricing configurations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('configuration/activate/:id')
  @ApiOperation({ summary: 'Activate a specific pricing configuration' })
  @ApiResponse({
    status: 200,
    description: 'Pricing configuration activated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Pricing configuration not found',
  })
  async activateConfiguration(
    @Param('id') id: number,
  ): Promise<ConfigurationActivationResponseDto> {
    try {
      const config = await this.pricingService.updatePricingConfiguration({
        id,
        isActive: true,
      });

      return {
        message: 'Pricing configuration activated successfully',
        configuration: config,
      };
    } catch (error) {
      throw new HttpException(
        'Failed to activate pricing configuration',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Check pricing service health' })
  @ApiResponse({
    status: 200,
    description: 'Pricing service health status',
  })
  async getHealthStatus(): Promise<PricingHealthStatusDto> {
    try {
      const config = await this.pricingService.getActivePricingConfiguration();
      
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        activeConfiguration: config.name,
        cacheStatus: 'active',
      };
    } catch (error) {
      return {
        status: 'degraded',
        timestamp: new Date().toISOString(),
        activeConfiguration: 'fallback',
        cacheStatus: 'error',
      };
    }
  }
}
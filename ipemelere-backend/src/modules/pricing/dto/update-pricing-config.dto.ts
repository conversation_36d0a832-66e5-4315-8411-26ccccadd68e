import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsBoolean,
  IsNumber,
  IsOptional,
  IsEnum,
  IsObject,
  IsDateString,
  Min,
  ValidateNested,
  IsNotEmpty,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { PricingModel, VehicleTypePricing, AdditionalPricingFactors } from '../entities/pricing-configuration.entity';

export class VehicleTypePriceDto {
  @ApiProperty({ 
    description: 'Base price for this vehicle type in MWK',
    example: 4000,
    minimum: 0 
  })
  @IsNumber()
  @Min(0, { message: 'Base price cannot be negative' })
  basePrice: number;

  @ApiProperty({ 
    description: 'Price per kilometer for this vehicle type in MWK',
    example: 600,
    minimum: 0 
  })
  @IsNumber()
  @Min(0, { message: 'Price per km cannot be negative' })
  pricePerKm: number;

  @ApiProperty({ 
    description: 'Minimum fare for this vehicle type in MWK',
    example: 3000,
    minimum: 0 
  })
  @IsNumber()
  @Min(0, { message: 'Minimum fare cannot be negative' })
  minimumFare: number;

  @ApiProperty({
    description: 'Surge multiplier for peak times',
    example: 1.5,
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1, { message: 'Surge multiplier must be at least 1' })
  surgeMultiplier?: number;
}

export class TimeBasedMultiplierDto {
  @ApiProperty({ 
    description: 'Start hour (0-23)',
    example: 17,
    minimum: 0,
    maximum: 23 
  })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => Math.floor(value))
  startHour: number;

  @ApiProperty({ 
    description: 'End hour (0-23)',
    example: 20,
    minimum: 0,
    maximum: 23 
  })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => Math.floor(value))
  endHour: number;

  @ApiProperty({ 
    description: 'Price multiplier for this time period',
    example: 1.3,
    minimum: 0.1 
  })
  @IsNumber()
  @Min(0.1, { message: 'Multiplier must be at least 0.1' })
  multiplier: number;

  @ApiProperty({
    description: 'Days of week (0=Sunday, 6=Saturday)',
    example: [1, 2, 3, 4, 5]
  })
  @IsOptional()
  @IsNumber({}, { each: true })
  dayOfWeek?: number[];
}

export class DistanceThresholdDto {
  @ApiProperty({ 
    description: 'Minimum distance in km',
    example: 0,
    minimum: 0 
  })
  @IsNumber()
  @Min(0)
  minDistance: number;

  @ApiProperty({ 
    description: 'Maximum distance in km',
    example: 10,
    minimum: 0 
  })
  @IsNumber()
  @Min(0)
  maxDistance: number;

  @ApiProperty({ 
    description: 'Price per km for this distance range',
    example: 500,
    minimum: 0 
  })
  @IsNumber()
  @Min(0)
  pricePerKm: number;
}

export class AdditionalPricingFactorsDto {
  @ApiProperty({
    description: 'Time-based pricing multipliers',
    type: [TimeBasedMultiplierDto]
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => TimeBasedMultiplierDto)
  timeOfDayMultipliers?: TimeBasedMultiplierDto[];

  @ApiProperty({
    description: 'Distance-based pricing thresholds',
    type: [DistanceThresholdDto]
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => DistanceThresholdDto)
  distanceThresholds?: DistanceThresholdDto[];

  @ApiProperty({
    description: 'General demand multiplier',
    example: 1.2,
    minimum: 0.1
  })
  @IsOptional()
  @IsNumber()
  @Min(0.1)
  demandMultiplier?: number;
}

export class UpdatePricingConfigDto {
  @ApiProperty({
    description: 'Unique name for the pricing configuration',
    example: 'peak_hours_pricing'
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty({ message: 'Name cannot be empty' })
  name?: string;

  @ApiProperty({
    description: 'Whether this configuration is active',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Pricing model to use',
    enum: PricingModel,
    example: PricingModel.VEHICLE_TYPE_BASED
  })
  @IsOptional()
  @IsEnum(PricingModel, { message: 'Invalid pricing model' })
  model?: PricingModel;

  @ApiProperty({
    description: 'Base price in Malawian Kwacha',
    example: 3000,
    minimum: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'Base price cannot be negative' })
  basePrice?: number;

  @ApiProperty({
    description: 'Price per kilometer in Malawian Kwacha',
    example: 3000,
    minimum: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'Price per km cannot be negative' })
  pricePerKm?: number;

  @ApiProperty({
    description: 'Vehicle-specific pricing configuration',
    type: 'object',
    additionalProperties: true
  })
  @IsOptional()
  @IsObject()
  vehicleTypePricing?: VehicleTypePricing;

  @ApiProperty({
    description: 'Additional pricing factors',
    type: AdditionalPricingFactorsDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AdditionalPricingFactorsDto)
  additionalFactors?: AdditionalPricingFactors;

  @ApiProperty({
    description: 'When this configuration becomes valid',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  validFrom?: Date;

  @ApiProperty({
    description: 'When this configuration expires',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  validTo?: Date;

  @ApiProperty({
    description: 'Description of this pricing configuration',
    example: 'Peak hours pricing with 30% surge'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Priority order (higher numbers have higher priority)',
    example: 1,
    minimum: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  priority?: number;

  // Internal field for updates
  @IsOptional()
  @IsNumber()
  id?: number;
}
import { ApiProperty } from '@nestjs/swagger';
import {
  IsNumber,
  IsOptional,
  IsEnum,
  IsDateString,
  Min,
  Max,
} from 'class-validator';
import { VehicleType } from '../../vehicles/entities/vehicle.entity';
import { PricingModel } from '../entities/pricing-configuration.entity';

export class PriceCalculationRequestDto {
  @ApiProperty({ 
    description: 'Pickup latitude',
    example: -15.7861,
    minimum: -90,
    maximum: 90 
  })
  @IsNumber()
  @Min(-90)
  @Max(90)
  pickupLatitude: number;

  @ApiProperty({ 
    description: 'Pickup longitude',
    example: 35.0058,
    minimum: -180,
    maximum: 180 
  })
  @IsNumber()
  @Min(-180)
  @Max(180)
  pickupLongitude: number;

  @ApiProperty({ 
    description: 'Dropoff latitude',
    example: -15.7931,
    minimum: -90,
    maximum: 90 
  })
  @IsNumber()
  @Min(-90)
  @Max(90)
  dropoffLatitude: number;

  @ApiProperty({ 
    description: 'Dropoff longitude',
    example: 35.0178,
    minimum: -180,
    maximum: 180 
  })
  @IsNumber()
  @Min(-180)
  @Max(180)
  dropoffLongitude: number;

  @ApiProperty({ 
    description: 'Vehicle type for pricing calculation',
    enum: VehicleType,
    example: VehicleType.SEDAN,
    required: false 
  })
  @IsOptional()
  @IsEnum(VehicleType)
  vehicleType?: VehicleType;

  @ApiProperty({ 
    description: 'Pre-calculated distance in kilometers',
    example: 5.2,
    minimum: 0,
    required: false 
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  distance?: number;

  @ApiProperty({ 
    description: 'Request time for time-based pricing',
    example: '2024-01-15T18:30:00Z',
    required: false 
  })
  @IsOptional()
  @IsDateString()
  requestTime?: Date;

  @ApiProperty({ 
    description: 'Number of passengers',
    example: 2,
    minimum: 1,
    maximum: 8,
    required: false 
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(8)
  passengerCount?: number;
}

export class PriceBreakdownDto {
  @ApiProperty({ 
    description: 'Base price component in MWK',
    example: 3000 
  })
  basePrice: number;

  @ApiProperty({ 
    description: 'Distance-based charge in MWK',
    example: 2600 
  })
  distanceCharge: number;

  @ApiProperty({ 
    description: 'Time-based multiplier applied',
    example: 1.3,
    required: false 
  })
  timeMultiplier?: number;

  @ApiProperty({ 
    description: 'Surge multiplier applied',
    example: 1.5,
    required: false 
  })
  surgeMultiplier?: number;

  @ApiProperty({ 
    description: 'Minimum fare enforced in MWK',
    example: 2500,
    required: false 
  })
  minimumFare?: number;

  @ApiProperty({ 
    description: 'Additional charges applied',
    type: 'array',
    items: { type: 'object' },
    required: false 
  })
  additionalCharges?: Array<{
    name: string;
    amount: number;
    description?: string;
  }>;
}

export class PriceCalculationResponseDto {
  @ApiProperty({ 
    description: 'Base price component in MWK',
    example: 3000 
  })
  basePrice: number;

  @ApiProperty({ 
    description: 'Distance-based price component in MWK',
    example: 2600 
  })
  distancePrice: number;

  @ApiProperty({ 
    description: 'Total calculated price in MWK',
    example: 5600 
  })
  totalPrice: number;

  @ApiProperty({ 
    description: 'Distance in kilometers',
    example: 5.2 
  })
  distance: number;

  @ApiProperty({ 
    description: 'Vehicle type used for calculation',
    enum: VehicleType,
    example: VehicleType.SEDAN,
    required: false 
  })
  vehicleType?: VehicleType;

  @ApiProperty({ 
    description: 'Pricing model used for calculation',
    enum: PricingModel,
    example: PricingModel.VEHICLE_TYPE_BASED 
  })
  pricingModel: PricingModel;

  @ApiProperty({ 
    description: 'Detailed price breakdown',
    type: PriceBreakdownDto 
  })
  breakdown: PriceBreakdownDto;

  @ApiProperty({ 
    description: 'Currency code',
    example: 'MWK' 
  })
  currency: string;
}
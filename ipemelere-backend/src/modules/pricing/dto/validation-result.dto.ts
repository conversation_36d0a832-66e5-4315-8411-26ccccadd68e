import { ApiProperty } from '@nestjs/swagger';

export class ValidationResult {
  @ApiProperty({ 
    description: 'Whether the configuration is valid',
    example: true 
  })
  isValid: boolean;

  @ApiProperty({ 
    description: 'List of validation errors',
    type: [String],
    example: ['Base price cannot be negative', 'Invalid vehicle type: invalid_type'] 
  })
  errors: string[];

  @ApiProperty({ 
    description: 'List of validation warnings',
    type: [String],
    example: ['Base price is quite low for Malawian Kwacha (< 1000 MWK)'] 
  })
  warnings: string[];
}

export class PricingHealthStatusDto {
  @ApiProperty({ 
    description: 'Service health status',
    example: 'healthy',
    enum: ['healthy', 'degraded', 'unhealthy'] 
  })
  status: string;

  @ApiProperty({ 
    description: 'Timestamp of health check',
    example: '2024-01-15T10:30:00Z' 
  })
  timestamp: string;

  @ApiProperty({ 
    description: 'Name of active pricing configuration',
    example: 'default' 
  })
  activeConfiguration: string;

  @ApiProperty({ 
    description: 'Cache status',
    example: 'active',
    enum: ['active', 'expired', 'error'] 
  })
  cacheStatus: string;
}

export class VehicleTypePricingUpdateResponseDto {
  @ApiProperty({ 
    description: 'Success message',
    example: 'Pricing for sedan updated successfully' 
  })
  message: string;

  @ApiProperty({ 
    description: 'Vehicle type that was updated',
    example: 'sedan' 
  })
  vehicleType: string;

  @ApiProperty({
    description: 'Updated pricing configuration',
    type: 'object',
    additionalProperties: true,
    properties: {
      basePrice: { type: 'number' },
      pricePerKm: { type: 'number' },
      minimumFare: { type: 'number' },
      surgeMultiplier: { type: 'number' }
    }
  })
  pricing: {
    basePrice: number;
    pricePerKm: number;
    minimumFare: number;
    surgeMultiplier?: number;
  };
}

export class ConfigurationActivationResponseDto {
  @ApiProperty({ 
    description: 'Success message',
    example: 'Pricing configuration activated successfully' 
  })
  message: string;

  @ApiProperty({
    description: 'Activated configuration details',
    type: 'object',
    additionalProperties: true,
    properties: {
      id: { type: 'number' },
      name: { type: 'string' },
      isActive: { type: 'boolean' },
      model: { type: 'string' },
      basePrice: { type: 'number' },
      pricePerKm: { type: 'number' }
    }
  })
  configuration: {
    id: number;
    name: string;
    isActive: boolean;
    model: string;
    basePrice: number;
    pricePerKm: number;
  };
}
#!/usr/bin/env node

/**
 * Manual Notification Test
 *
 * This script tests if we can manually trigger a ride notification
 */

const { io } = require('socket.io-client');
const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const API_BASE = `${BASE_URL}/api/v1`;

async function testManualNotification() {
  console.log('🧪 Testing Manual Ride Notification...\n');

  try {
    // Login as driver
    const driverLogin = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    const driverToken = driverLogin.data.token;
    console.log('✅ Driver logged in');

    // Connect to WebSocket
    const driverSocket = io(`${BASE_URL}/driver-availability`, {
      auth: { token: driverToken },
      transports: ['websocket']
    });

    driverSocket.on('connection_established', () => {
      console.log('✅ WebSocket connected');

      // Subscribe to ride requests
      driverSocket.emit('subscribe_to_ride_requests', {
        latitude: -15.78853,
        longitude: 35.00494,
        maxRadius: 25
      });
    });

    driverSocket.on('ride_requests_subscription_confirmed', () => {
      console.log('✅ Subscribed to ride requests');
      console.log('🔍 Waiting for ride notifications...');
      console.log('   (You can now create a ride from another terminal)');
    });

    driverSocket.on('new_ride_request', (data) => {
      console.log('🎉 RIDE NOTIFICATION RECEIVED!');
      console.log('Ride Data:', JSON.stringify(data, null, 2));
    });

    driverSocket.onAny((event, ...args) => {
      console.log(`📨 WebSocket Event [${event}]:`, args);
    });

    // Keep the connection alive
    setInterval(() => {
      console.log('💓 Still listening for notifications...');
    }, 30000);

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testManualNotification();
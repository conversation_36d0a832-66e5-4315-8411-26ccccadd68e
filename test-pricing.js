/**
 * Test script to verify the new pricing calculations
 * New pricing: 3000 MWK base + 3000 MWK per kilometer
 */

// Test cases for the new pricing model
const testCases = [
  {
    description: "1 km ride",
    distance: 1,
    expectedPrice: 3000 + (1 * 3000), // 6000 MWK
  },
  {
    description: "2.5 km ride", 
    distance: 2.5,
    expectedPrice: 3000 + (2.5 * 3000), // 10500 MWK
  },
  {
    description: "5 km ride",
    distance: 5,
    expectedPrice: 3000 + (5 * 3000), // 18000 MWK
  },
  {
    description: "10 km ride",
    distance: 10,
    expectedPrice: 3000 + (10 * 3000), // 33000 MWK
  },
  {
    description: "0.5 km ride (short trip)",
    distance: 0.5,
    expectedPrice: 3000 + (0.5 * 3000), // 4500 MWK
  }
];

console.log("=== New Pricing Model Test Cases ===");
console.log("Base Price: 3000 MWK");
console.log("Price per KM: 3000 MWK");
console.log("Formula: Base + (Distance × Rate per KM)");
console.log("=====================================\n");

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.description}`);
  console.log(`Distance: ${testCase.distance} km`);
  console.log(`Expected Price: ${testCase.expectedPrice} MWK`);
  console.log(`Calculation: 3000 + (${testCase.distance} × 3000) = ${testCase.expectedPrice} MWK`);
  console.log("---");
});

console.log("\n=== Comparison with Old Pricing ===");
console.log("Old pricing: 3000 MWK base + 500 MWK per km");
console.log("New pricing: 3000 MWK base + 3000 MWK per km");
console.log("=====================================\n");

testCases.forEach((testCase, index) => {
  const oldPrice = 3000 + (testCase.distance * 500);
  const newPrice = testCase.expectedPrice;
  const increase = newPrice - oldPrice;
  const percentIncrease = ((increase / oldPrice) * 100).toFixed(1);
  
  console.log(`${testCase.description}:`);
  console.log(`  Old: ${oldPrice} MWK`);
  console.log(`  New: ${newPrice} MWK`);
  console.log(`  Increase: ${increase} MWK (${percentIncrease}%)`);
  console.log("---");
});

# Implementation Plan

- [ ] 1. Create pricing configuration entity and supporting types
  - Create PricingConfiguration entity with all required fields
  - Define PricingModel enum and supporting interfaces (VehicleTypePricing, VehicleTypePrice, AdditionalPricingFactors)
  - Create database migration for pricing_configurations table
  - _Requirements: 1.1, 1.2, 2.1, 2.2_

- [x] 2. Implement core pricing service
  - Create PricingService with pricing calculation methods
  - Implement calculateRidePrice method for both vehicle-type and distance-only models
  - Add getRideOptions method to replace hardcoded pricing in search
  - Implement configuration management methods (getActivePricingConfiguration, updatePricingConfiguration)
  - Add fallback mechanism with 3000 MWK base price when no configuration is found
  - _Requirements: 3.1, 3.2, 1.3, 2.3_

- [x] 3. Create pricing management API endpoints
  - Create PricingController with admin authentication guards
  - Implement GET /admin/pricing/configuration endpoint
  - Implement PUT /admin/pricing/configuration endpoint with validation
  - Add POST /admin/pricing/configuration/validate endpoint
  - Create vehicle type specific pricing endpoints
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 4. Create DTOs and validation
  - Create UpdatePricingConfigDto with proper validation decorators
  - Create PriceCalculationRequest and PriceCalculationResponse DTOs
  - Add ValidationResult DTO for configuration validation
  - Implement comprehensive validation for all pricing inputs
  - _Requirements: 4.4, 3.3_

- [x] 5. Create pricing module and integrate dependencies
  - Create PricingModule with proper imports and exports
  - Register PricingService and PricingController
  - Add TypeORM repository for PricingConfiguration
  - Configure module dependencies and exports
  - _Requirements: 3.1, 3.2_

- [ ] 6. Create database migration and seeder
  - Write TypeORM migration to create pricing_configurations table
  - Create seeder to populate default pricing configuration with Malawian Kwacha values
  - Set default base price to 3000 MWK when no configuration exists
  - Migrate existing hardcoded values to database configuration with proper MWK conversion
  - Test migration rollback functionality
  - _Requirements: 1.1, 1.4_

- [ ] 7. Update RidesService to use PricingService
  - Replace hardcoded pricing methods with PricingService calls
  - Update searchRides method to use PricingService.getRideOptions
  - Modify calculatePrice method to use PricingService.calculateRidePrice
  - Remove hardcoded pricing constants and methods
  - Update error handling to use pricing service fallbacks
  - _Requirements: 5.1, 5.2, 3.1_

- [ ] 8. Add comprehensive unit tests for pricing service
  - Test vehicle-type-based pricing calculations
  - Test distance-only pricing calculations
  - Test configuration management methods
  - Test fallback mechanism when configuration is missing
  - Test error handling for invalid pricing data
  - _Requirements: 3.3, 1.4, 5.4_

- [ ] 9. Add integration tests for pricing API
  - Test pricing configuration CRUD operations
  - Test admin authentication and authorization
  - Test pricing calculation integration with rides service
  - Test database persistence and retrieval
  - Test validation error responses
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [ ] 10. Add end-to-end tests for pricing functionality
  - Test complete ride search with dynamic pricing
  - Test ride booking with updated pricing calculations
  - Test pricing consistency between search and booking
  - Test admin pricing updates reflecting in ride calculations
  - Test system behavior when pricing service fails
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [ ] 11. Update existing tests and fix breaking changes
  - Update RidesService tests to mock PricingService
  - Fix any existing tests that depend on hardcoded pricing
  - Update test data to work with new pricing system
  - Ensure all existing functionality still works
  - _Requirements: 5.1, 5.2_

- [ ] 12. Add caching and performance optimizations
  - Implement in-memory caching for active pricing configuration
  - Add cache invalidation on configuration updates
  - Optimize database queries for pricing configuration retrieval
  - Add performance monitoring for pricing calculations
  - _Requirements: 1.3, 3.2_
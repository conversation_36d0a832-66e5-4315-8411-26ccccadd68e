# Design Document

## Overview

The dynamic vehicle pricing system will replace hardcoded pricing logic with a flexible, database-driven configuration system. This design supports both vehicle-type-specific pricing and distance-based pricing models, allowing for runtime price adjustments without code deployments.

## Architecture

### Core Components

1. **PricingConfiguration Entity** - Stores pricing rules and configurations
2. **PricingService** - Centralized pricing calculation logic
3. **PricingController** - Admin API for pricing management
4. **PricingModule** - Module organization and dependency injection

### Data Flow

```
Ride Request → PricingService → PricingConfiguration → Price Calculation → Response
```

## Components and Interfaces

### 1. PricingConfiguration Entity

```typescript
@Entity('pricing_configurations')
export class PricingConfiguration extends BaseEntity {
  @Column({ unique: true })
  name: string; // e.g., 'default', 'peak_hours', 'weekend'
  
  @Column({ default: true })
  isActive: boolean;
  
  @Column({ type: 'enum', enum: PricingModel })
  model: PricingModel; // VEHICLE_TYPE_BASED, DISTANCE_ONLY
  
  @Column({ type: 'decimal', precision: 8, scale: 2 })
  basePrice: number; // Base price for distance-only model
  
  @Column({ type: 'decimal', precision: 8, scale: 2 })
  pricePerKm: number; // Price per km for distance-only model
  
  @Column({ type: 'json', nullable: true })
  vehicleTypePricing: VehicleTypePricing; // Vehicle-specific pricing
  
  @Column({ type: 'json', nullable: true })
  additionalFactors: AdditionalPricingFactors; // Future extensibility
  
  @Column({ type: 'timestamp', nullable: true })
  validFrom: Date;
  
  @Column({ type: 'timestamp', nullable: true })
  validTo: Date;
}
```

### 2. Supporting Types and Interfaces

```typescript
export enum PricingModel {
  VEHICLE_TYPE_BASED = 'vehicle_type_based',
  DISTANCE_ONLY = 'distance_only',
  HYBRID = 'hybrid'
}

export interface VehicleTypePricing {
  [VehicleType.SEDAN]: VehicleTypePrice;
  [VehicleType.SUV]: VehicleTypePrice;
  [VehicleType.HATCHBACK]: VehicleTypePrice;
  [VehicleType.LUXURY]: VehicleTypePrice;
  // ... other vehicle types
}

export interface VehicleTypePrice {
  basePrice: number;
  pricePerKm: number;
  minimumFare: number;
  surgeMultiplier?: number;
}

export interface AdditionalPricingFactors {
  timeOfDayMultipliers?: TimeBasedMultiplier[];
  distanceThresholds?: DistanceThreshold[];
  demandMultiplier?: number;
}
```

### 3. PricingService Interface

```typescript
@Injectable()
export class PricingService {
  // Core pricing calculation
  async calculateRidePrice(request: PriceCalculationRequest): Promise<PriceCalculationResponse>;
  
  // Get available pricing options for ride search
  async getRideOptions(searchRequest: SearchRideDto): Promise<RideOptionDto[]>;
  
  // Configuration management
  async getActivePricingConfiguration(): Promise<PricingConfiguration>;
  async updatePricingConfiguration(config: UpdatePricingConfigDto): Promise<PricingConfiguration>;
  
  // Pricing model detection
  private determinePricingModel(availableVehicles: Vehicle[]): PricingModel;
  private calculateVehicleTypePrice(vehicleType: VehicleType, distance: number): number;
  private calculateDistanceOnlyPrice(distance: number): number;
}
```

### 4. API Endpoints

```typescript
@Controller('admin/pricing')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin')
export class PricingController {
  @Get('configuration')
  async getCurrentConfiguration(): Promise<PricingConfiguration>;
  
  @Put('configuration')
  async updateConfiguration(@Body() config: UpdatePricingConfigDto): Promise<PricingConfiguration>;
  
  @Post('configuration/validate')
  async validateConfiguration(@Body() config: UpdatePricingConfigDto): Promise<ValidationResult>;
  
  @Get('vehicle-types')
  async getVehicleTypePricing(): Promise<VehicleTypePricing>;
  
  @Put('vehicle-types/:type')
  async updateVehicleTypePrice(@Param('type') type: VehicleType, @Body() price: VehicleTypePrice): Promise<void>;
}
```

## Data Models

### Database Schema Changes

1. **New Table: pricing_configurations**
   - Stores all pricing configurations with versioning support
   - Supports multiple active configurations for A/B testing

2. **Migration Strategy**
   - Create pricing_configurations table
   - Seed with current hardcoded values as default configuration
   - Update existing code to use PricingService

### Default Configuration Seeding

```sql
INSERT INTO pricing_configurations (name, isActive, model, basePrice, pricePerKm, vehicleTypePricing) 
VALUES (
  'default',
  true,
  'vehicle_type_based',
  50.00,
  15.00,
  '{
    "sedan": {"basePrice": 75, "pricePerKm": 20, "minimumFare": 50},
    "suv": {"basePrice": 100, "pricePerKm": 25, "minimumFare": 75},
    "hatchback": {"basePrice": 50, "pricePerKm": 15, "minimumFare": 40},
    "luxury": {"basePrice": 150, "pricePerKm": 35, "minimumFare": 100}
  }'
);
```

## Error Handling

### Pricing Service Error Scenarios

1. **No Active Configuration Found**
   - Fallback to hardcoded default values
   - Log warning for admin attention

2. **Invalid Pricing Data**
   - Validate all pricing inputs
   - Return clear error messages
   - Prevent negative or zero prices

3. **Configuration Update Failures**
   - Atomic updates with rollback capability
   - Validation before applying changes
   - Audit trail for pricing changes

### Fallback Strategy

```typescript
private getFallbackPricing(): PricingConfiguration {
  return {
    name: 'emergency_fallback',
    model: PricingModel.DISTANCE_ONLY,
    basePrice: 50,
    pricePerKm: 15,
    // ... other fallback values
  };
}
```

## Testing Strategy

### Unit Tests
- PricingService calculation accuracy
- Configuration validation logic
- Error handling scenarios
- Fallback mechanism testing

### Integration Tests
- Database configuration persistence
- API endpoint functionality
- Service integration with RidesService
- Migration and seeding verification

### Test Scenarios
1. **Vehicle Type Based Pricing**
   - Different vehicle types return correct prices
   - Distance calculation accuracy
   - Minimum fare enforcement

2. **Distance Only Pricing**
   - Base price + distance calculation
   - No vehicle type dependency
   - Consistent pricing across requests

3. **Configuration Management**
   - Admin can update pricing
   - Changes reflect immediately
   - Invalid configurations rejected

4. **Fallback Behavior**
   - System continues working when configuration fails
   - Appropriate logging and alerts
   - Graceful degradation

## Performance Considerations

### Caching Strategy
- Cache active pricing configuration in memory
- Invalidate cache on configuration updates
- Use Redis for distributed caching if needed

### Database Optimization
- Index on isActive and validFrom/validTo columns
- Efficient JSON queries for vehicle type pricing
- Regular cleanup of old configurations

### Monitoring
- Track pricing calculation performance
- Monitor configuration change frequency
- Alert on fallback usage

## Security Considerations

### Access Control
- Admin-only access to pricing management
- Audit trail for all pricing changes
- Rate limiting on configuration updates

### Data Validation
- Strict validation of pricing inputs
- Prevent injection attacks on JSON fields
- Sanitize all user inputs

### Configuration Integrity
- Backup configurations before updates
- Rollback capability for failed updates
- Version control for pricing configurations